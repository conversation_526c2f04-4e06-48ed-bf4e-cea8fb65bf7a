# ResearchAssistant

智能科研助手

## 介绍

这是一个基于Python的智能科研助手，可以自动完成各种科研任务，如数据处理、模型训练、模型评估、模型部署等。

### 文件目录

- backend    后端
- frontend   前端

## 技术栈

### 后端

- Python 3.12
- FastAPI
- LangChain
- LangGraph

### 前端

- Vue3.js
- Element-UI-plus
- Axios
- vite

环境要求：
Node.js ≥ 18.x（推荐≥20.x） 主流 LTS 版本
Vue ≥ 3.3.X Vue 3 正式版
pnpm ≥ 10.X pnpm 安装

建议安装nvm, 通过nvm安装node和pnpm，管理本地的node版本  

## 快速开始

### 后端

> 后端采用uv 管理工具，建议先安装uv
> 后端依赖postgreSQL数据库、redis, 建议采用docker安装这些环境.
> 项目中以及配置了开发环境的docker-compose.yml文件，可以一键启动相关环境， 在`backend/docker/docker-compose-dev.yml`, 执行`docker-compose -f docker-compose-dev.yml up -d`启动

- 1. 创建虚拟环境

```bash

# 进入后端项目目录
cd ./backend
```

```bash
# 使用uv管理器
uv venv --python 3.12


# 激活虚拟环境（Linux/Mac）
source .venv/bin/activate
# 激活虚拟环境（Windows）
.venv\Scripts\activate

```

- 2. 安装依赖包

```bash
# 通过uv 安装
uv sync --index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 仅同步生产依赖
uv sync --production

```

- 3. 配置环境变量

```bash
cp .env.example .env
```

然后设置使用的模型，配置对应的apikey

- 4. 运行

```bash

uv run ./app/main.py
```

开发的时候可以吧"gradio>=5.38.0",加进去，生产环境可以去掉

### 前端

pnpm config set registry <https://registry.npmmirror.com/>

## 相关文档学习

1. [Python 3.12](https://www.python.org/downloads/release/python-312/)
2. [FastAPI](https://fastapi.tiangolo.com/zh/tutorial/query-params/)  
3. [Element-UI-plus](https://element-plus.org/zh-CN/guide/quickstart.html)
4. [Vite](https://vitejs.dev/guide/)
5. [Axios](https://axios-http.com/zh/)
6. [LangChain](https://python.langchain.com/en/latest/index.html)]
7. [LangGraph](https://github.com/langchain-ai/langgraph)]
8. [LangChain CN](https://python.langchain.com.cn/docs/modules/memory/integrations/postgres_chat_message_history)