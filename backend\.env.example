IS_LOCAL=false # 是否是本地环境

# openai:调用gpt模型,oneapi:调用oneapi方案支持的模型,ollama:调用本地开源大模型,qwen:调用阿里通义千问大模型
# 模型提供者
LLM_PROVIDER_NAME="openrouter"  # 大语言模型模型提供者名称, 可选： deepseek, openai, azure, huggingface, openrouter

# 嵌入模型， 格式：模型提供者^模型名称
EMBEDDING_MODEL_NAME="siliconflow^BAAI/bge-m3"
# # 对话模型， 可以和推理模型一致
CHAT_MODEL_NAME="openrouter^qwen/qwen3-32b:free"
# # 推理模型，如果对话模型支持推理，则可以和对话模型一致
REASONER_MODEL_NAME="openrouter^deepseek/deepseek-r1-0528:free"


# openrouter^qwen/qwen3-32b:free
# openrouter^qwen/qwen3-14b:free
# openrouter^qwen/qwen3-30b-a3b:free
# openrouter^deepseek/deepseek-chat-v3-0324:free
# 对话模型， 格式：模型提供者^模型名称
# CHAT_MODEL_NAME="deepseek^deepseek-chat"
# 推理模型， 格式：模型提供者^模型名称
# REASONER_MODEL_NAME="deepseek^deepseek-reasoner"

# deepseek
DEEPSEEK_API_KEY=xxxx

# openrouter
OPENROUTER_API_KEY=

# siliconflow 硅基流动
SILICONFLOW_API_KEY=

# dashscope 阿里百炼
DASHSCOPE_API_KEY=

# openai
OPENAI_API_KEY=
OPENAI_CHAT_MODEL=gpt-3.5-turbo


# 存储配置
MEMORY_TYPE=memory  # 短期记忆存储类型。可选: redis, memory, postgres, es, 默认memory
STATE_TYPE=memory   # 状态存储类型，长期记忆存储类型，可以与MEMORY_TYPE不同，可选: redis, memory, postgres, es， 默认memory
HISTORY_STORE_TYPE=file # 会话历史存储类型。可选: redis, postgres, es, file 默认file

# Redis
REDIS_URL=redis://localhost:6379/0

# PostgreSQL
PG_DB_URI="postgresql://postgres:postgres798@localhost:54322/postgres?sslmode=disable"

# ES
ES_URL=http://localhost:9200
ES_INDEX_NAME=chatbot
ES_USER=elastic
ES_PASSWORD=changeme


# File
FILE_PATH=/Users/<USER>/workspace/work_for_code/research-assistant/history/

# 认证
SECRET_KEY=your_secret_key
ALGORITHM=HS256


# websearhch
WEB_SEARCH_PROVIDER = "tavily"  # 在线搜索， 支持 tavily、bocha

# 博查app_key
BOCHA_API_KEY = "sss"

# tavily搜索
TAVILY_API_KEY="sss"