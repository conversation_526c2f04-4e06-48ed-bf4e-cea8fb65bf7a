

# 科研助手后端


## 运行环境

1. Python 3.12
2. uv 管理工具


## 快速启动


1. 创建虚拟环境

```bash

# 进入后端项目目录
cd ./backend
```

```bash
# 使用uv管理器
uv venv --python 3.12


# 激活虚拟环境（Linux/Mac）
source .venv/bin/activate
# 激活虚拟环境（Windows）
.venv\Scripts\activate
```
2. 安装依赖包

```bash
# 通过uv 安装
uv sync --index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 仅同步生产依赖
uv sync --production
```

3. 配置环境变量

```bash
cp .env.example .env
```
然后设置使用的模型

4. 运行

```bash
uv run main.py
```


开发的时候可以吧"gradio>=5.38.0",加进去，生产环境可以去掉

## 项目目录结构


```bash

chatbot-project/
├── .env                    # 环境变量配置
├── .gitignore
├── README.md
├── requirements.txt        # Python依赖
├── pyproject.toml          # 项目配置
│
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── main.py             # FastAPI主应用入口
│   ├── config.py           # 应用配置
│   ├── dependencies.py     # FastAPI依赖项
│   │
│   ├── api/                # API路由
│   │   ├── __init__.py
│   │   ├── v1/             # API版本控制
│   │   │   ├── __init__.py
│   │   │   ├── chat.py     # 聊天相关路由
│   │   │   └── tools.py    # 工具调用路由
│   │
│   ├── core/               # 核心功能
│   │   ├── __init__.py
│   │   ├── memory.py       # 记忆/历史记录处理
│   │   ├── streaming.py    # 流式处理逻辑
│   │   └── state.py        # 对话状态管理
│   │
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── schemas.py      # Pydantic模型
│   │   └── database.py     # 数据库模型（如果使用ORM）
│   │
│   ├── assistants/         # 各种助手实现
│   │   ├── __init__.py
│   │   ├── base.py         # 基础助手类
│   │   ├── chat.py         # 通用聊天助手
│   │   ├── research.py     # 深度研究助手
│   │   ├── data_analysis.py # 数据分析助手
│   │   └── query.py        # 查询助手
│   │
│   ├── tools/              # 自定义工具
│   │   ├── __init__.py
│   │   ├── base.py         # 基础工具类
│   │   ├── calculator.py   # 计算工具示例
│   │   ├── web_search.py   # 网络搜索工具
│   │   └── custom_mcp.py   # 自定义MCP实现
│   │
│   ├── graphs/             # LangGraph实现
│   │   ├── __init__.py
│   │   ├── base.py         # 基础图结构
│   │   ├── chat.py         # 聊天流程图
│   │   └── multi_agent.py  # 多代理流程图（未来扩展）
│   │
│   ├── llms/           # 模型封装
│   │   ├── __init__.py
│   │   ├── llm.py          # LLM服务封装
│   │
│   └── utils/              # 实用工具
│       ├── __init__.py
│       ├── logger.py       # 日志配置
│       └── helpers.py      # 辅助函数
│
├── tests/                  # 测试目录
│   ├── __init__.py
│   ├── test_api.py
│   ├── test_assistants.py
│   └── test_tools.py
│
└── scripts/                # 脚本目录
    ├── setup_db.py         # 数据库初始化脚本
    └── deploy.py           # 部署脚本

```
