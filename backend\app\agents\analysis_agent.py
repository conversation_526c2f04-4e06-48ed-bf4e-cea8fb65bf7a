from langchain.prompts import ChatPromptTemplate
from app.tools.web_search import bocha_websearch_tool
from langchain.agents import initialize_agent, Tool, AgentType

class WebSearchAgent:
    """
    一个用于分析的类。
    从提交的数据分析提起信息，进行分析和总结。
    """
    def __init__(self, llm):
        """
        初始化 SummaryChain 类，并加载指定的语言模型。
    
        参数:
            model_name (str): 要加载的语言模型的名称。
        """
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_template(
            "You are a professional assistant specializing in extracting keywords from user questions and chat histories. Extract keywords and connect them with spaces to output a efficient and precise search query. Be careful not answer the question directly, just output the search query.\n\nHistories: {history}\n\nQuestion: {question}"
        )
        # self.chain = self.prompt | self.llm
        # 创建LangChain工具
        self.bocha_tool = Tool(
            name="BochaWebSearch",
            func=bocha_websearch_tool,
            description="使用Bocha Web Search API 进行搜索互联网网页，输入应为搜索查询字符串，输出将返回搜索结果的详细信息，包括网页标题、网页URL、网页摘要、网站名称、网站Icon、网页发布时间等。"
        )


    def invoke(self, input_data):
        """
        使用提供的输入数据调用链以生成搜索查询。

        参数:
            input_data (dict): 包含 'history' 和 'question' 键的字典。

        返回:
            str: 链生成的搜索查询。
        """
        # 初始化代理，包含您的自定义工具
        search_agent = initialize_agent(
            tools=[self.bocha_tool],
            llm=self.llm,
            agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True
        )
        response = search_agent.run(input_data)
        print(response)

        return response


