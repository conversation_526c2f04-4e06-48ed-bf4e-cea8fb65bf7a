from langchain.prompts import ChatPromptTemplate
from langchain.agents import initialize_agent, Too<PERSON>, AgentType

import os

WEB_SEARCH_PROVIDER = os.getenv("WEB_SEARCH_PROVIDER", "tavily")

class WebSearchAgent:
    """
    一个用于生成搜索查询的类。
    从用户问题和聊天记录中提取关键词，并生成高效的搜索查询。
    """
    def __init__(self, llm):
        """
        初始化 SummaryChain 类，并加载指定的语言模型。
    
        参数:
            model_name (str): 要加载的语言模型的名称。
        """
        self.llm = llm
        self.prompt = ChatPromptTemplate.from_template(
            "You are a professional assistant specializing in extracting keywords from user questions and chat histories. Extract keywords and connect them with spaces to output a efficient and precise search query. Be careful not answer the question directly, just output the search query.\n\nHistories: {history}\n\nQuestion: {question}"
        )
        # self.chain = self.prompt | self.llm
        web_search_tool = None
        if WEB_SEARCH_PROVIDER == "bocha":
            from app.tools.web_search import bocha_websearch_tool

            web_search_tool = bocha_websearch_tool

        elif WEB_SEARCH_PROVIDER == "tavily":
            from langchain_tavily import TavilySearch

            web_search_tool = TavilySearch(
                    max_results=5,
                    topic="general",
                    # include_answer=False,
                    # include_raw_content=False,
                    # include_images=False,
                    # include_image_descriptions=False,
                    # search_depth="basic",
                    # time_range="day",
                    # include_domains=None,
                    # exclude_domains=None
                )
        else:
            raise Exception("Invalid search type")
        

        # 创建LangChain工具
        self.tool = Tool(
            name="WebSearch",
            func=web_search_tool,
            description="使用Web Search API 进行搜索互联网网页，输入应为搜索查询字符串，输出将返回搜索结果的详细信息，包括网页标题、网页URL、网页摘要、网站名称、网站Icon、网页发布时间等。"
        )


    def invoke(self, input_data):
        """
        使用提供的输入数据调用链以生成搜索查询。

        参数:
            input_data (dict): 包含 'history' 和 'question' 键的字典。

        返回:
            str: 链生成的搜索查询。
        """
        # 初始化代理，包含您的自定义工具
        search_agent = initialize_agent(
            tools=[self.tool],
            llm=self.llm,
            agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True
        )
        response = search_agent.run(input_data)
        print(response)

        return response


