from fastapi import APIRouter, Depends, HTTPException, Body, Path, Query
from fastapi.responses import JSONResponse, StreamingResponse
from app.core.response import Message, ChatCompletionResponse, ConversationRequest, ChatCompletionRequest, format_response
from app.assistants.base import Conversation
import logging
import json
import uuid
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Annotated
import os
from app.prompts.template import get_prompt_template
from starlette_context import context
import app.globals as globals

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get('/assistants')
def get_assistants():
    """
    获取助手列表
    返回格式：
    {
        "code": 0,
        "message": "success", 
        "data": [
            {
                "id": "助手唯一ID",
                "label": "助手显示名称",
                "description": "助手描述", 
                "avatar": "助手头像(emoji)",
                "specialty": "专业领域",
                "capabilities": ["能力1", "能力2"],
                "tags": ["标签1", "标签2"],
                "createdAt": "创建时间",
                "isActive": "是否激活"
            }
        ]
    }
    """
    try:
        # 读取助手数据文件
        data_file = os.path.join(os.path.dirname(__file__), 'assistants.json')
        
        if not os.path.exists(data_file):
            return JSONResponse(content={
                'code': 1, 
                'message': 'Assistants data file not found'
            },status_code=404), 404
        
        with open(data_file, 'r', encoding='utf-8') as f:
            assistants = json.load(f)
        
        # 只返回激活的助手
        active_assistants = [assistant for assistant in assistants if assistant.get('isActive', True)]
        
        return JSONResponse({
            'code': 0,
            'message': 'success',
            'data': active_assistants
        })
        
    except Exception as e:
        return JSONResponse(content={'code': 1, 'message': str(e)}, status_code=500), 500

@router.post('/assistants/conversation')
def add_get_assistant_conversation(request: ConversationRequest):
    """
    创建新会话
    
    agrg:
        - headers = {"Content-Type": "application/json"}
    
        - data = {
            "name": "xxx",
            "createdAt": "",
            "userId": "2231333",
            "conversationId": "3232133"
            "assistantId": "123"
        }
    """
    if not request.assistantId:
        return JSONResponse(content={
            'code': 1, 
            'message': 'Assistant ID is required'
        }, status_code=400), 400
    if not request.name or request.name is None or request.name == '':
        request.name = "新对话"

    chat_assistant = globals.get_chat_assistant(request.assistantId)
    CURRENT_TIME = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    conversation = Conversation(
        conversation_id= request.conversationId,
        name = request.name,
        created_at=CURRENT_TIME,
        assistant_id=request.assistantId,
        user_id=request.userId
    )
    chat_assistant.conversation_history.add_history(conversation)

    return JSONResponse({
        'code': 0,
        'message': 'success',
        'data': {
        'conversation_id': request.conversationId,
        'name': request.name,
        'created_at': CURRENT_TIME,
        'assistant_id': request.assistantId,
        'user_id': request.userId
       }
    })

@router.post('/assistants/conversation/del/{assistantId}/{conversationId}')
def delete_assistant_conversation(assistantId: Annotated[str, Path(title="assistantId")],
                                  conversationId: Annotated[str, Path(title="conversationId")]):
    """
    删除指定ID对话
    args:
        - assistantId: 应用助手id(路径参数)
        - conversationId. 会话id(路径参数)
    """
    chat_assistant = globals.get_chat_assistant(assistantId)
    chat_assistant.conversation_history.delete_history_by_id(conversationId)

@router.get('/assistants/conversation/rename/{assistantId}/{conversationId}')
def update_assistant_conversation(assistantId: Annotated[str, Path(title="assistantId")],
                                  conversationId: Annotated[str, Path(title="conversationId")],
                                  name: str):
    """
    修改指定ID对话的名称
    args:
        - assistantId: 应用助手id(路径参数)
        - conversationId. 会话id(路径参数)
        - name：新的名称
    """
    chat_assistant = globals.get_chat_assistant(assistantId)
    
    newname = name[:20]
    # 更新会话时间
    # conversation.updated_at = CURRENT_TIME
    chat_assistant.conversation_history.update_history(conversationId, newname)

@router.get('/assistants/{assistantId}/conversations')
def get_assistant_conversations(assistantId: Annotated[str, Path(title="assistantId")],
                                 userId: str, limit: int = 20, page: int = 1):
    """
    获取指定助手的对话历史列表
    支持分页和懒加载
    """
    try:        
        if not assistantId:
            return JSONResponse(content={
                'code': 1, 
                'message': 'Assistant ID is required'
            }, status_code=400), 400
        
        assistant_conversations = []
        total = 0
 
        chat_assistant = globals.get_chat_assistant(assistantId)

        assistant_conversations, total = chat_assistant.conversation_history.get_history_by_page(
                user_id=userId, 
                assistant_id=assistantId, page=page, page_size=limit)
        
        # 按创建时间降序排列
        # assistant_conversations.sort(key=lambda x: x['created_at'], reverse=True)
  
        assistant_conversations_dict = []
        #  把assistant_conversations转成字典
        if len(assistant_conversations) > 0:
            if isinstance(assistant_conversations[0], dict) :
                assistant_conversations_dict = [
                    {
                        'conversation_id': conv['conversation_id'],
                        'user_id': conv['user_id'],
                        'assistant_id': conv['assistant_id'],
                        'name': conv['name'],
                        'created_at': conv['created_at'],
                        'group': get_time_group(conv['created_at'])
                    } for conv in assistant_conversations
                ]
            else:
                assistant_conversations_dict = [
                    {
                        'conversation_id': conv.conversation_id,
                        'user_id': conv.user_id,
                        'assistant_id': conv.assistant_id,
                        'name': conv.name,
                        'created_at': conv.created_at,
                        'group': get_time_group(conv.created_at)
                    } for conv in assistant_conversations
                ]
        

        return JSONResponse({
            'code': 0,
            'message': 'success',
            'data': {
                'conversations': assistant_conversations_dict,
                'pagination': {
                    'page': page ,
                    'limit': limit,
                    'total': total,
                    'has_more': total > (limit * page)
                }
            }
        })
            
    except Exception as e:
        return JSONResponse(content={'code': 1, 'message': str(e)}, status_code=500), 500
    

@router.get("/assistants/completions/history")
async def conversation_history(threadId: str, assistantId: str = "chat_assistant", userId: str = "user"):
    """获取历史消息"""
    try:  
        if not assistantId:
            return JSONResponse(content={
                'code': 1, 
                'message': 'Assistant ID is required'
            }, status_code=400), 400

        chat_assistant = globals.get_chat_assistant(assistantId)
        messages = chat_assistant.get_messages(threadId)
        

        return JSONResponse({
            'code': 0,
            'message': 'success',
            'data': messages
        })
    except Exception as e:
        return JSONResponse(content={'code': 1, 'message': str(e)}, status_code=500), 500


def get_time_group(created_at):
    """根据创建时间确定时间分组"""
    try:
        created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        now = datetime.now()
        
        # 转换为本地时间比较
        created_date = created_time.date()
        today = now.date()
        yesterday = today - timedelta(days=1)
        
        if created_date == today:
            return 'today'
        elif created_date == yesterday:
            return 'yesterday'
        elif created_date >= today - timedelta(days=7):
            return 'thisWeek'
        elif created_date >= today - timedelta(days=30):
            return 'thisMonth'
        else:
            return 'older'
    except:
        return 'older'