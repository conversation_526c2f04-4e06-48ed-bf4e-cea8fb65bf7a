from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import JSONResponse, StreamingResponse
from app.core.response import Message, ChatCompletionResponse, ChatCompletionResponseChoice, ChatCompletionRequest, format_response
from app.assistants.base import Conversation
import logging
import json
import uuid
import time
import datetime

from starlette_context import context
import app.globals as globals

router = APIRouter()
logger = logging.getLogger(__name__)






# 封装POST请求接口，与大模型进行问答
@router.post("/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """
    请求参数说明：
    headers = {"Content-Type": "application/json"}
    data = {
        "messages": [{"role": "user", "content": user_message}],
        "stream": true,
        "userId": "2231333",
        "conversationId": "3232133"
        "assistantId": "123"
    }
    """

    if request.assistantId is None:
        raise HTTPException(status_code=500, detail="assistantId参数不能为空")
    # 在 chat_completions 函数中获取 chatAssistantGraph
    # chat_assistant = globals.chat_assistant
    # if not chat_assistant:
    #     raise Exception("请先初始化 chat_assistant")
    
    # if request.assistantId != 'chat_assistant':
    #     raise Exception("当前只支持chat_assistant")
    
    chat_assistant = globals.get_chat_assistant(request.assistantId)
    chat_assistant_graph = chat_assistant.get_graph()

    # 判断初始化是否完成
    if not chat_assistant_graph:
        logger.error("服务未初始化")
        raise HTTPException(status_code=500, detail="服务未初始化")

    try:
        logger.info(f"收到聊天完成请求: {request}")

        query_prompt = request.messages[-1].content
        logger.info(f"用户问题是: {query_prompt}")

        config = {"configurable": {"thread_id": request.userId+"@@"+request.conversationId, "user_id": request.userId, "assistant_id": request.assistantId}}
        logger.info(f"用户当前会话信息: {config}")
        CURRENT_TIME = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 查询会话并更新时间
        conversation = chat_assistant.conversation_history.get_history_by_id(request.conversationId)

        if conversation is None:
            #  创建会话
            conversation = Conversation(
                conversation_id= request.conversationId,
                name = query_prompt[:20],
                created_at=CURRENT_TIME,
                assistant_id=request.assistantId,
                user_id=request.userId
            )
            chat_assistant.conversation_history.add_history(conversation)
        else:

            if conversation['name'] == "新对话":
                # TODO AI 生成一个话题名称， 暂时先截取前20个字
                newname = query_prompt[:20]
                # 更新会话时间
                # conversation.updated_at = CURRENT_TIME
                chat_assistant.conversation_history.update_history(request.conversationId, newname)
        prompt = []
        prompt_template_system = chat_assistant.get_system_prompt({
             "CURRENT_TIME": CURRENT_TIME
        })

        if prompt_template_system is not None:
            prompt.append({"role": "system", "content": prompt_template_system})


        prompt_template_user = chat_assistant.get_user_prompt({
            "CURRENT_TIME": CURRENT_TIME,
            "query": query_prompt
        })

        if prompt_template_user is None:
            prompt = [{
                "role": "user",
                "content": query_prompt
            }]
        else:
            prompt.append({"role": "user", "content": prompt_template_user})


        if len(prompt) == 0:
            prompt = [{
                "role": "user",
                "content": query_prompt
            }]

        chat_assistant.create_message_history(request.conversationId).add_user_message(query_prompt)
        
        # 处理流式响应
        if request.stream:
    
            async def generate_stream(conversationId: str = None):
                chunk_id = f"chatcmpl-{uuid.uuid4().hex}"
                #  定义一个变量用来存储最终的响应
              
                all_chunks = []
                for message_chunk, metadata in chat_assistant_graph.stream({"messages": prompt}, config, stream_mode="messages"):
                    chunk = message_chunk.content

                    #如果空字符，直接下一次循环
                    if not chunk or chunk == "":
                        continue
            
                    all_chunks.append(chunk)  # 存储所有块
                    logger.info(f"chunk: {chunk}")
                    # 在处理过程中产生每个块
                    # 构造 SSE 格式的数据块（确保是字符串）
                    data = json.dumps({
                        "id": chunk_id,
                        "object": "chat.completion.chunk",
                        "conversationId": conversationId,
                        "created": int(time.time()),
                        "choices": [{
                            "index": 0,
                            "delta": {"content": chunk},
                            "finish_reason": None
                        }]
                    })
                    # 显式编码为 UTF-8 字节（可选，取决于你的框架）
                    encoded_chunk = f"data: {data}\n\n".encode("utf-8")
       
                    # 返回 UTF-8 编码的字节（或直接返回字符串，取决于框架）
                    yield encoded_chunk  # 或者直接 yield f"data: {data}\n\n"
                 
                # 流结束的最后一块
                end_data = json.dumps({
                    "id": chunk_id,
                    "object": "chat.completion.chunk",
                    "conversationId": conversationId,
                    "created": int(time.time()),
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": "stop"
                    }]
                })
             

                last_encoded_chunk = f"data: {end_data}\n\n".encode("utf-8")
                all_messages = "".join(all_chunks)
        
                chat_assistant.create_message_history(request.conversationId).add_ai_message(all_messages)
        
                yield last_encoded_chunk  # 或者直接返回字符串

            
            # 返回fastapi.responses中StreamingResponse对象
            return StreamingResponse(generate_stream(request.conversationId), media_type="text/event-stream")

        # 处理非流式响应处理
        else:
            try:
          
                events = chat_assistant_graph.stream({"messages": prompt}, config)

                for event in events:
                    for value in event.values():
                        result =  value["messages"][-1].content
            except Exception as e:
                logger.info(f"Error processing response: {str(e)}")

            formatted_response = str(format_response(result))
            logger.info(f"格式化的搜索结果: {formatted_response}")
            
            chat_assistant.create_message_history(request.conversationId).add_ai_message(formatted_response)
        
            response = ChatCompletionResponse(
                choices=[
                    ChatCompletionResponseChoice(
                        index=0,
                        message=Message(role="assistant", content=formatted_response),
                        finish_reason="stop"
                    )
                ]
            )
            logger.info(f"发送响应内容: \n{response}")
            # 返回fastapi.responses中JSONResponse对象
            # model_dump()方法通常用于将Pydantic模型实例的内容转换为一个标准的Python字典，以便进行序列化
            return JSONResponse(content=response.model_dump())

    except Exception as e:
        # 输出完整的错误信息
  
        logger.error(f"处理聊天完成时出错:\n\n {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
