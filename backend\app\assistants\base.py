
from abc import ABC, abstractmethod
from langgraph.graph import StateGraph, START, END, MessagesState
from typing import List
from datetime import datetime
import logging
import os
from app.database.models.conversation import Conversation
from langchain_core.chat_history import BaseChatMessageHistory
from app.database.conversation_record.base_assistant_history import BaseAssistantHistory


logger = logging.getLogger(__name__)



class BaseAssistant(ABC):
    def __init__(self, assistant_id: str = "default"):
    
        self.assistant_id = assistant_id
        # 消息历史存储方式
        self.history_store_type = os.getenv("HISTORY_STORE_TYPE", "file")
        self.conversation_history = self.create_conv_history()

    @abstractmethod
    def get_system_prompt(self, variables: dict)-> str:
        """
        获取system提示词
        """
        pass

    @abstractmethod
    def get_user_prompt(self, variables: dict)-> str:
        """
        获取user提示词
        """
        pass

    @abstractmethod
    def get_graph(self):
        pass

    @abstractmethod
    def create_graph(self, llm, checkpointer, in_postgres_store) -> StateGraph:
        """create_graph"""
        pass

    def create_conv_history(self)-> BaseAssistantHistory:
        """create_conv_history"""
        if self.history_store_type == "file":
            from app.database.conversation_record.file_assistant_history import FileAssistantHistory
            file_path = os.getenv("FILE_PATH", "conv-history.json")
            return FileAssistantHistory(
                assistant_id=self.assistant_id, 
                file_path=file_path + self.assistant_id
            )
        elif self.history_store_type == "postgres":
            # return PostgresConversationHistory(assistant_id=self.assistant_id)
        # else:
            raise ValueError("Invalid history store type")

    def create_message_history(self, session_id: str)-> BaseChatMessageHistory:
        """
        创建消息历史
        Args:
            session_id: 会话id, 相当于conversation_id
        参考文档：https://python.langchain.com/api_reference/community/chat_message_histories.html
        """

        if self.history_store_type == "file":
            from langchain_community.chat_message_histories.file import FileChatMessageHistory
            file_path = os.getenv("FILE_PATH", "message-history.json")
            chat_message_history = FileChatMessageHistory(
                file_path=file_path + session_id
            )

        elif self.history_store_type == "es":
            from langchain_community.chat_message_histories.elasticsearch import ElasticsearchChatMessageHistory

            chat_message_history = ElasticsearchChatMessageHistory(
               session_id=session_id
            )
        elif self.history_store_type == "redis":
            # pip install redis
            from langchain_community.chat_message_histories.redis import RedisChatMessageHistory

            url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
            chat_message_history = RedisChatMessageHistory(
                session_id=session_id,
                url=url,
            )
        elif self.history_store_type == "postgres":
            from langchain_community.chat_message_histories.postgres import PostgresChatMessageHistory

            DB_URI = os.getenv("PG_DB_URI", "postgresql://postgres:postgres@localhost:5432/postgres?sslmode=disable") 
            chat_message_history = PostgresChatMessageHistory(
                session_id==session_id,
                connection_string=DB_URI,
            )

        else:
            raise ValueError(f"Invalid history store type: {self.history_store_type}")

        return chat_message_history

    def get_messages(self, session_id: str):
        messages = self.create_message_history(session_id=session_id).messages
        #  序列号成字典

        messages_dict = []
        for m in messages:
            if not isinstance(m, dict):
                # if isinstance(m, Hu)
                if m.type == 'human': 
                    messages_dict.append({
                        "type": m.type,
                        "data": {
                            "content": m.content,
                  
                        }
                    })
                elif m.type == 'ai':
                    messages_dict.append({
                        "type": m.type,
                        "data":  {
                            "content": m.content
                        }
                    })

            else:
                messages_dict.append(m)
        # 按create_at 倒序排列


        return messages_dict


    def save_graph_visualization(self, graph: StateGraph, filename: str = None) -> None:
        """
        将构建的graph可视化保存为 PNG 文件
        """
        try:
            if filename is None:
                filename = self.assistant_id + "_graph.png"
            
            with open(filename, "wb") as f:
                f.write(graph.get_graph().draw_mermaid_png())
            logger.info(f"Graph visualization saved as {filename}")
        except IOError as e:
            logger.info(f"Warning: Failed to save graph visualization: {str(e)}")
