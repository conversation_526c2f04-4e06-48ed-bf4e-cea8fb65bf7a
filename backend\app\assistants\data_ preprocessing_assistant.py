from app.assistants.base import BaseAssistant, Conversation
from langgraph.graph import StateGraph, START, END, MessagesState
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.tools import tool
from langchain_community.tools import ShellTool
from langchain_community.agent_toolkits import create_python_agent
from langchain_experimental.tools import PythonREPLTool
from typing import Dict, TypedDict, List
from datetime import datetime
import logging
import uuid
from psycopg_pool import ConnectionPool
import pandas as pd

from app.tools.data_preprocessing_tools import *

class AgentState(TypedDict):
    messages: List[BaseMessage]
    data: pd.DataFrame
    metadata: Dict
    processed_data: pd.DataFrame
    preprocessing_steps: List[str]

# 初始化状态
initial_state = {
    "messages": [],
    "data": None,
    "metadata": {},
    "processed_data": None,
    "preprocessing_steps": []
}

class DataProcessingAssistant(BaseAssistant):
    """数据处理助手"""
    def __init__(self, llm, embedding, checkpointer, in_postgres_store: BaseStore, 
                 connection_pool: ConnectionPool = None,
                 assistant_id: str = "data_processing_assistant"):
        super().__init__(assistant_id, connection_pool)
        self.llm = llm
        self.embedding = embedding
        self.checkpointer = checkpointer
        self.in_postgres_store = in_postgres_store
        # 定义 Graph
        self.graph = self.create_graph(llm, checkpointer, in_postgres_store)

        self.workflow = self.build_workflow(llm)


    def get_graph(self):
        return self.graph


       # 创建和配置 chatbot 的状态图
    def create_graph(self, llm, checkpointer, in_postgres_store: BaseStore) -> StateGraph:
        from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
        try:
            
            prompt = ChatPromptTemplate.from_messages([
                ("system", system_message),
                MessagesPlaceholder(variable_name="messages"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ])
    
            agent = create_tool_calling_agent(llm, tools, prompt)
            agent_executor = AgentExecutor(agent=agent, tools=tools)
        except Exception as e:
            raise RuntimeError(f"创建 graph 失败: {str(e)}")
        
    def agent_node(self, state, agent, name):
        """Agent节点执行函数"""
        result = agent.invoke(state)
        return {"messages": [HumanMessage(content=result["output"], name=name)]}
    
    def setup_agents(self, llm: BaseChatModel):
        """设置所有Agent"""
        # 数据加载和检查Agent
        data_inspection_tools = [load_data, detect_data_types, generate_report, visualize_data]
        data_inspection_agent = self.create_agent(
            llm,
            data_inspection_tools,
            """你是一个数据检查专家。负责加载数据并执行初始数据检查。
            检查内容包括：数据类型检测、缺失值检测、生成数据报告等。
            完成后将数据传递给清洗Agent。"""
        )
        
        # 数据清洗Agent
        data_cleaning_tools = [handle_missing_values, detect_outliers, handle_outliers]
        data_cleaning_agent = self.create_agent(
            llm,
            data_cleaning_tools,
            """你是一个数据清洗专家。负责处理数据中的问题，包括：
            1. 处理缺失值（填充、删除等）
            2. 检测和处理异常值
            完成后将数据传递给转换Agent。"""
        )
    
        # 数据转换Agent
        data_transformation_tools = [encode_categorical, normalize_data]
        data_transformation_agent = self.create_agent(
            llm,
            data_transformation_tools,
            """你是一个数据转换专家。负责：
            1. 分类变量编码（One-Hot, Label Encoding等）
            2. 数据标准化/归一化
            3. 特征工程（如需要）
            完成后将数据传递给评估Agent。"""
        )
    
        # 数据评估和分割Agent
        data_evaluation_tools = [split_dataset, visualize_data, save_processed_data]
        data_evaluation_agent = self.create_agent(
            llm,
            data_evaluation_tools,
            """你是数据评估和分割专家。负责：
            1. 评估数据质量
            2. 将数据分割为训练集、验证集和测试集
            3. 保存处理后的数据"""
        )
        
        # 代码生成和执行Agent
        python_agent = create_python_agent(
            llm=llm,
            tool=PythonREPLTool(),
            agent_type="tool-calling",
            verbose=True
        )

        return {
            "data_inspection_agent": data_inspection_agent,
            "data_cleaning_agent": data_cleaning_agent,
            "data_transformation_agent": data_transformation_agent,
            "data_evaluation_agent": data_evaluation_agent,
            "python_agent": python_agent
        }
    
    def build_workflow(self, llm: BaseChatModel):
        """构建预处理工作流"""
        agents = self.setup_agents(llm)
               # 构建 graph
    
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("data_inspection", lambda state: self.agent_node(state, agents["data_inspection_agent"], "data_inspection"))
        workflow.add_node("data_cleaning", lambda state: self.agent_node(state, agents["data_cleaning_agent"], "data_cleaning"))
        workflow.add_node("data_transformation", lambda state: self.agent_node(state, agents["data_transformation_agent"], "data_transformation"))
        workflow.add_node("data_evaluation", lambda state: self.agent_node(state, agents["data_evaluation_agent"], "data_evaluation"))
        workflow.add_node("python_execution", lambda state: self.agent_node(state, agents["python_agent"], "python_execution"))
        
        # 设置入口点
        workflow.set_entry_point("data_inspection")
        
        # 添加边
        workflow.add_edge("data_inspection", "data_cleaning")
        workflow.add_edge("data_cleaning", "data_transformation")
        workflow.add_edge("data_transformation", "data_evaluation")
        workflow.add_edge("data_evaluation", END)
        
        # 条件边（可根据需要添加条件分支）
        # workflow.add_conditional_edges(...)
        
        return workflow.compile()