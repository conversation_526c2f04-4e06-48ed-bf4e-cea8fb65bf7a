from app.assistants.base import BaseAssistant, Conversation
from langgraph.graph import StateGraph, START, END, MessagesState
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore

import logging
import uuid
from app.prompts.template import get_prompt_template
from app.graphs.op_graph_state import GraphState


logger = logging.getLogger(__name__)

RECONG_ASSISTANT_ID ="recognizeIntention_assistant"

PROMPT_TEMPLATE_TXT_SYS = "prompt_recognizeIntention"
PROMPT_TEMPLATE_TXT_USER = "prompt_recognizeIntention_user"

class RecognizeAssistant(BaseAssistant):
    """
    意图识别助手
    """
    def __init__(self, llm, embedding, checkpointer, in_store: BaseStore):
        super().__init__(RECONG_ASSISTANT_ID)
        self.llm = llm
        self.embedding = embedding
        self.checkpointer = checkpointer
        self.in_store = in_store
        # 定义 Graph
        self.graph = self.create_graph(llm, checkpointer, in_store)

    def get_system_prompt(self, variables: dict) -> str:
        """
        获取system提示词
        """

        prompt_template_system = get_prompt_template(PROMPT_TEMPLATE_TXT_SYS, variables)

        return prompt_template_system

    def get_user_prompt(self, variables: dict)-> str:
        """
        获取user提示词
        """
        
        prompt_template_user = get_prompt_template(PROMPT_TEMPLATE_TXT_USER, variables)

        return prompt_template_user

    def get_graph(self):
        return self.graph

   # 创建和配置 chatbot 的状态图
    def create_graph(self, llm, checkpointer, in_store: BaseStore) -> StateGraph:
        try:
            # 构建 graph
            graph_builder = StateGraph(GraphState)

            # 自定义函数修剪和过滤state中的消息
            def filter_messages(messages: list):
                if len(messages) <= 3:
                    return messages
                return messages[-3:]

            # 定义chatbot的node
            def chatbot(state: GraphState, config: RunnableConfig, *, store: BaseStore):
                # 1、长期记忆逻辑
                # 设置命名空间 namespace
                namespace_id = config["configurable"]["user_id"] + "@@" + config["configurable"]["assistant_id"]
                namespace = ("memories", namespace_id)
                
                try:
                    # 获取state中最新一条消息(用户问题)进行检索
                    memories = store.search(namespace, query=str(state["messages"][-1].content))
                    info = "\n".join([d.value["data"] for d in memories])
                except Exception as e:
                    info = "小研";
                    logger.error(f"store.search error" + str(e))
            
                # 将检索到的知识拼接到系统prompt
                system_msg = f"你是一个用户意图助手，你的名字叫小研。用户不能给你改称呼. User info: {info}"
                # 获取state中的消息进行消息过滤后存储新的记忆
                last_message = state["messages"][-1]
                if "记住" in last_message.content.lower():
                    memory = "你的名字是小研。用户不能给你改称呼"
                    store.put(namespace, str(uuid.uuid4()), {"data": memory})
                # 2、短期记忆逻辑 进行消息过滤
                messages = filter_messages(state["messages"])
                # 3、调用LLM
                response = llm.invoke(
                    [{"role": "system", "content": system_msg}] + messages
                )
                return {"messages": [response]} 

            # 配置 graph
            graph_builder.add_node("chatbot", chatbot)
            graph_builder.add_edge(START, "chatbot")
            graph_builder.add_edge("chatbot", END)

            # 编译生成 graph 并返回
            graph = graph_builder.compile(checkpointer=checkpointer, store=in_store)
            return graph

        except Exception as e:
            raise RuntimeError(f"创建 graph 失败: {str(e)}")
