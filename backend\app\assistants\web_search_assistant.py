from app.assistants.base import BaseAssistant
from langgraph.graph import StateGraph, START, END

from langgraph.store.base import BaseStore
from langchain_core.messages import AIMessage, HumanMessage

import logging
from app.prompts.template import get_prompt_template
from app.graphs.op_graph_state import GraphState

from app.agents.websearch_agent import WebSearchAgent


logger = logging.getLogger(__name__)

RECONG_ASSISTANT_ID ="recognizeIntention_assistant"

PROMPT_TEMPLATE_TXT_SYS = "prompt_web_search"
PROMPT_TEMPLATE_TXT_USER = "prompt_web_search_user"

class WebSearchAssistant(BaseAssistant):
    """
    网络搜索助手
    """
    def __init__(self, llm, embedding, checkpointer, in_store: BaseStore):
        super().__init__(RECONG_ASSISTANT_ID)
        self.llm = llm
        self.embedding = embedding
        self.checkpointer = checkpointer
        self.in_store = in_store
        # 定义 Graph
        self.graph = self.create_graph(llm, checkpointer, in_store)

    def get_system_prompt(self, variables: dict) -> str:
        """
        获取system提示词
        """
        # return None
        prompt_template_system = get_prompt_template(PROMPT_TEMPLATE_TXT_SYS, variables)

        return prompt_template_system

    def get_user_prompt(self, variables: dict)-> str:
        """
        获取user提示词
        """
        # return None
        prompt_template_user = get_prompt_template(PROMPT_TEMPLATE_TXT_USER, variables)

        return prompt_template_user

    def get_graph(self):
        return self.graph

   # 创建和配置 chatbot 的状态图
    def create_graph(self, llm, checkpointer, in_store: BaseStore) -> StateGraph:
        try:
            # 构建 graph
            graph_builder = StateGraph(GraphState)

            search_agent = WebSearchAgent(llm)

            # 定义节点函数
            def search_node(state: GraphState):
                print("---执行搜索节点---")
                # query = state["web_query"]
                query=str(state["messages"][-1].content)
                # 使用搜索Agent获取结果, 实际可能先通过大模型生成几个搜索词进行搜索
                search_results = search_agent.invoke(query)
                return {
                    "messages": [HumanMessage(content=f"搜索完成，结果如下:\n{search_results}")],
                    "search_results": search_results
                }
            
            def report_node(state: GraphState):
                print("---执行报告生成节点---")
                analysis = next(m for m in state["messages"] if isinstance(m, AIMessage) and "分析完成" in m.content)
                
                # 构建报告提示
                prompt = f"""
                根据以下分析结果，生成一份专业的商业报告:
                
                {analysis.content}
                
                报告格式要求:
                1. 标题
                2. 执行摘要(100字以内)
                3. 详细分析(分章节)
                4. 结论与建议
                5. 参考文献(如果有)
                """
                
                report = report_llm.invoke(prompt)
                return {
                    "messages": [HumanMessage(content=f"最终报告:\n{report.content}")]
                }

            # 配置 graph
            # 添加节点
            graph_builder.add_node("search", search_node)
            graph_builder.add_edge(START, "search")
            graph_builder.add_edge("search", END)
            
            # 编译生成 graph 并返回
            graph = graph_builder.compile(checkpointer=checkpointer, store=in_store)
            return graph

        except Exception as e:
            raise RuntimeError(f"创建 graph 失败: {str(e)}")
