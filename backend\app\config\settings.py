import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    is_local = os.getenv("IS_LOCAL", "false").lower() == "true"
    llm_provider = os.getenv("PROVIDER_NAME", "deepseek").lower()
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    MYSQL_URL = os.getenv("MYSQL_URL", "mysql+pymysql://root:123456@localhost:3306/langgraph")
    
    # 记忆配置
    MAX_HISTORY_LENGTH = 50
    MAX_SHORT_TERM_LENGTH = 10
    MAX_LONG_TERM_LENGTH = 5

settings = Settings()