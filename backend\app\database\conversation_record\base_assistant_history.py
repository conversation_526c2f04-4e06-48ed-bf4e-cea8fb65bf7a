import os
from typing import List,TYPE_CHECKING
from app.database.models.conversation import Conversation

from abc import ABC, abstractmethod
from collections.abc import Sequence
from datetime import datetime

def messages_to_dict_seq(messages: Sequence[Conversation]) -> list[dict]:
        """Convert a sequence of Messages to a list of dictionaries.

        Args:
            messages: Sequence of messages (as BaseMessages) to convert.

        Returns:
            List of messages as dicts.
        """
        return [message_to_dict(m) for m in messages]

def message_to_dict(message: Conversation) -> dict:
        """Convert a Message to a dictionary.

        Args:
            message: Message to convert.

        Returns:
            Message as a dict. The dict will have a "type" key with the message type
            and a "data" key with the message data as a dict.
        """
        return {
                "conversation_id": message.conversation_id,
                "user_id": message.user_id,
                "assistant_id": message.assistant_id,
                "name": message.name,
                "created_at":  message.created_at
             }


class BaseAssistantHistory:
    def __init__(self, assistant_id: str = "default"):
    
        self.assistant_id = assistant_id
        self.history_store_type = os.getenv("HISTORY_STORE_TYPE", "flie")


    def add_history(self, conversation: Conversation)-> None:
        pass

    @abstractmethod
    def get_history_by_id(self, conversation_id:str) -> Conversation:
        pass

    @abstractmethod
    def get_history_by_page(self, user_id:str, assistant_id:str, page, page_size) -> List[Conversation]:
        pass

    def update_history(self, conversation_id: str, name: str):
        return None
    
    def delete_history_by_id(self, session_id: str) -> None:
        return None
    
    def clear_history(self) -> None:
        return None
    

