
import os
from paginate import Page
from typing import List
import json
from pathlib import Path
from app.database.models.conversation import Conversation
from app.database.conversation_record.base_assistant_history import BaseAssistantHistory, messages_to_dict_seq


class FileAssistantHistory(BaseAssistantHistory):
    def __init__(self, assistant_id: str = "default", file_path: str = "conversations.json", encoding: str = "utf-8", ensure_ascii: bool = False):
        super().__init__(assistant_id)
        self.file_path = Path(file_path)
        self.encoding = encoding
        self.ensure_ascii = ensure_ascii

        if not self.file_path.exists():
            self.file_path.touch()
            self.file_path.write_text(
                json.dumps([], ensure_ascii=self.ensure_ascii), encoding=self.encoding
            )
    def load_history(self) -> List[Conversation]:
        """加载历史对话"""
        with self.file_path.open(mode='r', encoding=self.encoding) as f:
            return [Conversation(**message) for message in json.load(f)]
        
        
    
    def add_history(self, conversation: Conversation)-> None:
        messages = messages_to_dict_seq(self.load_history())
        messages.append(messages_to_dict_seq([conversation])[0])
        self.file_path.write_text(
            json.dumps(messages, ensure_ascii=self.ensure_ascii), encoding=self.encoding
        )
    def get_history_by_id(self, conversation_id:str) -> Conversation:  
        """Retrieve the messages from the local file"""
        messages = messages_to_dict_seq(self.load_history())

        # 过滤
        messages = [message for message in messages if message["conversation_id"] == conversation_id]
        if len(messages) > 0:
            return messages[0]

        return None

    def get_history_by_page(self, user_id:str, assistant_id:str, page, page_size) -> List[Conversation]:
        messages = messages_to_dict_seq(self.load_history())
        
        # 分页过滤
        messages = [message for message in messages if message["user_id"] == user_id and message["assistant_id"] == assistant_id]
        messages.sort(key=lambda x: x['created_at'], reverse=True)
        def pagination(page_size, page, data):
            start = (page - 1) * page_size
            end = page * page_size
            return data[start:end]
        messages = pagination(page_size, page, messages)
        return messages, len(messages)

    def update_history(self, conversation_id: str, name: str):
        messages = messages_to_dict_seq(self.load_history())
        # 修改指定id的会话名称
        for message in messages:
            if message["conversation_id"] == conversation_id:
                message["name"] = name
                break
        return None
    
    def delete_history_by_id(self, conversation_id: str) -> None:
        messages = messages_to_dict_seq(self.load_history())

        # 删除指定id的会话
        messages = [message for message in messages if message["conversation_id"] != conversation_id]
        self.file_path.write_text(
            json.dumps(messages, ensure_ascii=self.ensure_ascii), encoding=self.encoding
        )
    
    def clear_history(self) -> None:
        self.file_path.write_text(
            json.dumps([], ensure_ascii=self.ensure_ascii), encoding=self.encoding
        )