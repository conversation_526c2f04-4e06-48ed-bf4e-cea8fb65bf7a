# import json
# import logging
# import datetime

# from typing import List, Union
# from psycopg_pool import ConnectionPool

# from app.database.models.conversation import Conversation
# from langchain_core._api import deprecated
# from langchain_core.chat_history import BaseChatMessageHistory
# from psycopg import sql

# from langchain_core.messages import (
#     AIMessage,
#     BaseMessage,
#     HumanMessage,
#     message_to_dict,
#     messages_from_dict,
# )

# logger = logging.getLogger(__name__)

# DEFAULT_CONNECTION_STRING = "postgresql://postgres:mypassword@localhost/chat_history"
# DEFAULT_HISTORY_TABLE_NAME = "conversation_history"
# # from langchain_postgres import PostgresChatMessageHistory;
# @deprecated(
#     since="0.0.31",
#     message=(
#         "This class is deprecated and will be removed in a future version. "
#         "You can swap to using the `PostgresChatMessageHistory`"
#         " implementation in `langchain_postgres`. "
#         "Please do not submit further PRs to this class."
#         "See <https://github.com/langchain-ai/langchain-postgres>"
#     ),
#     alternative="from langchain_postgres import PostgresChatMessageHistory;",
#     pending=True,
# )
# class CustomPostgresChatMessageHistory():
#     """Chat message history stored in a Postgres database.

#     **DEPRECATED**: This class is deprecated and will be removed in a future version.

#     Use the `PostgresChatMessageHistory` implementation in `langchain_postgres`.
#     """

#     def __init__(
#         self,connection: ConnectionPool=None,
#         connection_string: str = DEFAULT_CONNECTION_STRING, 
#         table_name: str = "message_store",
#     ):
#         import psycopg
#         from psycopg.rows import dict_row

#         try:
#             if connection is None:
#                 self.connection = psycopg.connect(connection_string)
#             else:
#                 self.connection = connection.getconn()

#             self.cursor = self.connection.cursor(row_factory=dict_row)
#         except psycopg.OperationalError as error:
#             logger.error(error)

#         self.table_name = table_name

#         self._create_table_if_not_exists()
#         self._create_his_table_if_not_exists()

#     def _create_table_if_not_exists(self) -> None:
#         create_table_query = f"""CREATE TABLE IF NOT EXISTS {self.table_name} (
#             id SERIAL PRIMARY KEY,
#             session_id TEXT NOT NULL,
#             message JSONB NOT NULL
#         );"""
#         self.cursor.execute(create_table_query)
#         self.connection.commit()

#     def _create_his_table_if_not_exists(self) -> None:
#         create_table_query = f"""CREATE TABLE IF NOT EXISTS {DEFAULT_HISTORY_TABLE_NAME} (
#             id SERIAL PRIMARY KEY,
#             conversation_id TEXT NOT NULL,
#             name text NOT NULL,
#             assistant_id text NOT NULL,
#             user_id text  NOT NULL,
#             created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL
#         );"""
#         self.cursor.execute(create_table_query)
#         self.connection.commit()

#     def backend/app/database/custom_postgres_chat_message_history.py(self, user_id: str, assistant_id:str, page: int = 1, page_size: int = 20): 
#         # 分页计算
#         offset = (page - 1) * page_size

#         count = (
#             sql.SQL("SELECT COUNT(*) FROM {} WHERE user_id = %s and assistant_id = %s;").format(
#                 sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#             )
#         )

#         self.cursor.execute(count, (user_id, assistant_id))

#         total = self.cursor.fetchone()["count"]

#         query = (
#             sql.SQL("SELECT * FROM {} WHERE user_id = %s and assistant_id = %s ORDER BY created_at DESC limit %s offset %s;").format(
#                 sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#             )
#         )
#         self.cursor.execute(query, (user_id, assistant_id, page_size, offset))

#         items = [record for record in self.cursor.fetchall()]
#         # items = 
#         # for record in self.cursor.fetchall():
#         #     items.
#         # items = [record["message"] for record in self.cursor.fetchall()]
#         # messages = messages_from_dict(items)
                
#         # 转换数据
#         conversations = []
#         for data in items:
        
#             conversation = Conversation(
#                 conversation_id=data['conversation_id'],
#                 assistant_id=data['assistant_id'],
#                 user_id=data['user_id'],
#                 name=data["name"],
#                 created_at=data["created_at"].strftime("%Y-%m-%d %H:%M:%S")
#             )
#             # curdata = conversation.dict()
#             conversations.append(conversation)

#         return conversations, total

#     def get_history_by_id(self, session_id: str) -> None:
#         query = sql.SQL("SELECT * FROM {} WHERE conversation_id = %s;").format(
#             sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#             )
#         self.cursor.execute(query, (session_id,))
#         return self.cursor.fetchall()

#     def add_history(self, conversation: Conversation):
#         """Append the history to the record in PostgreSQL"""

#         query = sql.SQL("INSERT INTO {} (conversation_id, name, assistant_id, user_id) VALUES (%s, %s, %s, %s);").format(
#             sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#         )
#         self.cursor.execute(
#             query, (conversation.conversation_id, 
#                     conversation.name, 
#                     conversation.assistant_id,
#                     conversation.user_id)
#                     )
        
#         self.connection.commit()

#     def update_history(self, conversation_id: str, name: str):
#         """Append the history to the record in PostgreSQL"""

#         query = sql.SQL("UPDATE {} SET name = %s WHERE conversation_id = %s;").format(
#             sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#         )
#         self.cursor.execute(
#             query, (name, conversation_id)
#         )
#     def delete_history_by_id(self, session_id: str) -> None:
#         """Clear session memory from PostgreSQL"""
#         query = sql.SQL("DELETE FROM {} WHERE conversation_id = %s;").format(
#             sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#         )
#         self.cursor.execute(query, (session_id,))
#         self.connection.commit()

#     def clear_history(self, user_id: str, assistant_id: str) -> None:
#         """Clear session memory from PostgreSQL"""

#         query = sql.SQL("DELETE FROM {} WHERE user_id = %s and assistant_id=%s;").format(
#             sql.Identifier(DEFAULT_HISTORY_TABLE_NAME)
#         )
#         self.cursor.execute(query, (user_id, assistant_id))
#         self.connection.commit()


#     def messages(self, session_id: str) -> List[BaseMessage]:  # type: ignore[override]
#         """Retrieve the messages from PostgreSQL"""

#         query = (
#            sql.SQL("SELECT message FROM {} WHERE session_id = %s ORDER BY id;").format(
#             sql.Identifier(self.table_name)
#         )
#         )
      
#         self.cursor.execute(query, (session_id,))
#         items = [record["message"] for record in self.cursor.fetchall()]
#         # messages = messages_from_dict(items)
#         return items

#     def add_user_message(self, session_id: str, message: Union[HumanMessage, str]) -> None:
#         """Convenience method for adding a human message string to the store.

#         Please note that this is a convenience method. Code should favor the
#         bulk add_messages interface instead to save on round-trips to the underlying
#         persistence layer.

#         This method may be deprecated in a future release.

#         Args:
#             message: The human message to add to the store.
#         """
#         if isinstance(message, HumanMessage):
#             self.add_message(session_id, message)
#         else:
#             self.add_message(session_id, HumanMessage(content=message))

#     def add_ai_message(self, session_id: str, message: Union[AIMessage, str]) -> None:
#         """Convenience method for adding an AI message string to the store.

#         Please note that this is a convenience method. Code should favor the bulk
#         add_messages interface instead to save on round-trips to the underlying
#         persistence layer.

#         This method may be deprecated in a future release.

#         Args:
#             message: The AI message to add.
#         """
#         if isinstance(message, AIMessage):
#             self.add_message(session_id, message)
#         else:
#             self.add_message(session_id, AIMessage(content=message))

#     def add_message(self, session_id: str, message: BaseMessage) -> None:
#         """Append the message to the record in PostgreSQL"""
#         from psycopg import sql

#         query = sql.SQL("INSERT INTO {} (session_id, message) VALUES (%s, %s);").format(
#             sql.Identifier(self.table_name)
#         )
#         self.cursor.execute(
#             query, (session_id, json.dumps(message_to_dict(message)))
#         )
#         self.connection.commit()

#     def clear(self, session_id: str) -> None:
#         """Clear session memory from PostgreSQL"""
#         query = sql.SQL("DELETE FROM {} WHERE session_id = %s;").format(
#             sql.Identifier(self.table_name)
#         )
#         self.cursor.execute(query, (session_id,))
#         self.connection.commit()

#     def __del__(self) -> None:
#         if self.cursor:
#             self.cursor.close()
#         if self.connection:
#             self.connection.close()
