from app.assistants.base import BaseAssistant
from app.assistants.chat_assistant import ChatAssistant
from psycopg_pool import ConnectionPool
from app.assistants.recognize_intention_assistant import RecognizeAssistant
from app.assistants.web_search_assistant import WebSearchAssistant

chat_assistant:ChatAssistant = None
connection_pool:ConnectionPool = None
recognize_intention_assistant: RecognizeAssistant = None
web_search_assistant: WebSearchAssistant = None

def get_chat_assistant(assistantId: str) -> BaseAssistant:
    """
    获取助手
    """
    assistant = None
    if assistantId == "chat_assistant":
        assistant = chat_assistant
        if not assistant:
            raise Exception("请先初始化 chat_assistant")
    elif assistantId == "recognize_assistant":
        assistant = recognize_intention_assistant
        if not assistant:
            raise Exception("请先初始化 recognize_assistant")
    elif assistantId == "web_search_assistant":
        assistant = web_search_assistant
        if not assistant:
            raise Exception("请先初始化 web_search_assistant")

    else:
        raise Exception("请指定正确的助手ID, 如果正确， 应该是没有注册助手")

    return assistant

def register_assistant(llm, embedding, checkpointer, in_store) -> None:
    """
    注册助手
    """
    import app.globals as globals
    
    # 注册chart 助手
    chatAssistant = ChatAssistant(llm, embedding, checkpointer, in_store)
    globals.chat_assistant = chatAssistant


    # 注册意图识别助手
    recognize_assistant = RecognizeAssistant(llm, embedding, checkpointer, in_store)
    globals.recognize_intention_assistant = recognize_assistant

    # 注册web搜索助手
    globals.web_search_assistant = WebSearchAssistant(llm, embedding, checkpointer, in_store)



    
