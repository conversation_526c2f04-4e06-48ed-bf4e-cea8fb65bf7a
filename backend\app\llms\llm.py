import os
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from typing import Optional
import logging
import requests
from dotenv import load_dotenv

# 加载 .env 文件（默认从当前目录查找）
load_dotenv()  

# Author:@南哥AGI研习社 (B站 or YouTube 搜索“南哥AGI研习社”)

os.environ["OPENAI_API_KEY"] = "NA"
# 设置日志模版
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# 模型配置字典
MODEL_CONFIGS = {
    "openai": {
        "base_url": "https://nangeai.top/v1",
        "api_key": os.getenv("OPENAI_API_KEY", "xxx"),
        "chat_model": "gpt-4o-mini",
        "embedding_model": "text-embedding-3-small"

    },
    "openrouter": {
        "base_url": "https://openrouter.ai/api/v1",
        "api_key": os.getenv("OPENROUTER_API_KEY", "xxx"),
        "chat_model": "qwen-max",
        "embedding_model": "text-embedding-v1"
    },
    "deepseek": {
        "base_url": "https://api.deepseek.com/v1",
        "api_key": os.getenv("DEEPSEEK_API_KEY", "xxx"),
        "chat_model": "qwen-max",
        "embedding_model": "text-embedding-v1"
    },
    "qwen": {
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": "sk-80a72f794bc4488d85798d590e96db43",
        "chat_model": "qwen-max",
        "embedding_model": "text-embedding-v1"
    },
    "ollama": {
        "base_url": "http://localhost:11434/v1",
        "api_key": "ollama",
        "chat_model": "deepseek-r1:14b",
        "embedding_model": "nomic-embed-text:latest"
    }
}


# 默认配置
DEFAULT_TEMPERATURE = 0.7


class LLMInitializationError(Exception):
    """自定义异常类用于LLM初始化错误"""
    pass


def initialize_llm(
        llm_type: str = None,
        model_name: str = None):
    """
    初始化LLM实例

    Args:
        llm_type (str): LLM类型，可选值为 'openai', 'oneapi', 'qwen', 'ollama'

    Returns:
        ChatOpenAI: 初始化后的LLM实例

    Raises:
        LLMInitializationError: 当LLM初始化失败时抛出
    """
    try:
        if llm_type is None:
            llm_type = "deepseek"

        if llm_type == "siliconflow":
            llm = ChatOpenAI(
                base_url="https://api.siliconflow.cn/v1",
                api_key=os.getenv("SILICONFLOW_API_KEY", "xxx"),
                model=model_name,
                temperature=DEFAULT_TEMPERATURE,
                timeout=30,  # 添加超时配置（秒）
                max_retries=2  # 添加重试次数
            )

            return llm
        
        
        # 检查llm_type是否有效
        if llm_type not in MODEL_CONFIGS:
            raise ValueError(f"不支持的LLM类型: {llm_type}. 可用的类型: {list(MODEL_CONFIGS.keys())}")

        config = MODEL_CONFIGS[llm_type]

        # 特殊处理ollama类型
        if llm_type == "ollama":
            os.environ["OPENAI_API_KEY"] = "NA"

        if model_name is None:
            model_name = config["chat_model"]


        # 创建LLM实例
        llm = ChatOpenAI(
            base_url=config["base_url"],
            api_key=config["api_key"],
            model= model_name,
            temperature=DEFAULT_TEMPERATURE,
            timeout=30,  # 添加超时配置（秒）
            max_retries=2  # 添加重试次数
        )

        logger.info(f"成功初始化 {llm_type} LLM")
        return llm

    except ValueError as ve:
        logger.error(f"LLM配置错误: {str(ve)}")
        raise LLMInitializationError(f"LLM配置错误: {str(ve)}")
    except Exception as e:
        logger.error(f"初始化LLM失败: {str(e)}")
        raise LLMInitializationError(f"初始化LLM失败: {str(e)}")

def initialize_embedding(
        llm_type: str = None,
        model_name: str = None):
    try:
        if llm_type is None:
            llm_type = "deepseek"


        if llm_type == "siliconflow":
            embedding = OpenAIEmbeddings(
                base_url="https://api.siliconflow.cn/v1",
                api_key=os.getenv("SILICONFLOW_API_KEY", "xxx"),
                model=model_name,
            )

            return embedding
        

        # 检查llm_type是否有效
        if llm_type not in MODEL_CONFIGS:
            raise ValueError(f"不支持的LLM embedding类型: {llm_type}. 可用的类型: {list(MODEL_CONFIGS.keys())}")

        config = MODEL_CONFIGS[llm_type]

        # 特殊处理ollama类型
        if llm_type == "ollama":
            os.environ["OPENAI_API_KEY"] = "NA"

        if model_name is None:
            model_name = config["embedding_model"]

      

        embedding = OpenAIEmbeddings(
            base_url=config["base_url"],
            api_key=config["api_key"],
            model=model_name,
            deployment=model_name
        )

        logger.info(f"成功初始化 {llm_type} LLM")
        return embedding

    except ValueError as ve:
        logger.error(f"LLM embedding配置错误: {str(ve)}")
        raise LLMInitializationError(f"LLM配置错误: {str(ve)}")
    except Exception as e:
        logger.error(f"初始化LLM失败: {str(e)}")
        raise LLMInitializationError(f"初始化LLM失败: {str(e)}")

def get_chat_llm(llm_type: str = None,model_name: str = None) -> ChatOpenAI:
    """获取对话LLM实例"""

    if llm_type is None:
        llm_type = os.getenv("CHAT_MODEL_NAME", "deepseek^deepseek-chat")

    if "^" in llm_type: 
        # ^ 分割
        llm_type, model_name = llm_type.split("^")

    try:
        return initialize_llm(llm_type, model_name)
    except LLMInitializationError as e:
        logger.warning(f"使用默认配置重试: {str(e)}")
        if llm_type != None:
            return initialize_llm(None, None)
        raise  # 如果默认配置也失败，则抛出异常

def get_reasoner_llm(llm_type: str = None, model_name: str = None) -> ChatOpenAI:
    """
    获取推理LLM实例

    Args:
        llm_type (str): LLM类型

    Returns:
        ChatOpenAI: LLM实例
    """
    if llm_type is None:
        llm_type = os.getenv("REASONER_MODEL_NAME", "deepseek^deepseek-reasoner")

    if "^" in llm_type: 
        # ^ 分割
        llm_type, model_name = llm_type.split("^")

    try:
        return initialize_llm(llm_type, model_name)
    except LLMInitializationError as e:
        logger.warning(f"使用默认配置重试: {str(e)}")
        if llm_type != None:
            return initialize_llm(None, None)
        raise  # 如果默认配置也失败，则抛出异常

def get_embedding(llm_type: str = None, model_name: str = None)  -> OpenAIEmbeddings:
    if llm_type is None:
        llm_type = os.getenv("EMBEDDING_MODEL_NAME", "text-embedding-3-small")

    if "^" in llm_type: 
        # ^ 分割
        llm_type, model_name = llm_type.split("^")
    
    try:
        return initialize_embedding(llm_type, model_name)
    except LLMInitializationError as e:
        logger.warning(f"使用默认配置重试: {str(e)}")
        if llm_type != None:
            return initialize_embedding(None, None)
        raise  # 如果默认配置也失败，则抛出异常


# 测试
if __name__ == "__main__":
    try:
        # 测试不同类型的LLM初始化
        embedding = get_embedding()
        texts = ["LangChain 是一个强大的框架", "文本嵌入模型可以将文本转换为向量"]
        # 嵌入文本
        embedded_texts = embedding.embed_documents(texts)

        print(embedded_texts)
        logger.info(embedded_texts)
    except LLMInitializationError as e:
        logger.error(f"程序终止: {str(e)}")