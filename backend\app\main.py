
import logging
from contextlib import asynccontextmanager
from pydantic import BaseModel, Field
from app.llms.llm import get_chat_llm, get_embedding, get_reasoner_llm

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.chat_assistant_api import router as chat_router
from app.api.v1.assistants_common_api import router as common_router
import uvicorn
from dotenv import load_dotenv
import app.globals as globals


# 加载 .env 文件（默认从当前目录查找）
load_dotenv()  

from app.utils.graph_store_utils import get_graph_store, get_graph_saver

# 设置LangSmith环境变量 进行应用跟踪，实时了解应用中的每一步发生了什么
# os.environ["LANGCHAIN_TRACING_V2"] = "true"
# os.environ["LANGCHAIN_API_KEY"] = "***************************************************"


# 设置日志模版
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# API服务设置相关
PORT = 8012

# 申明全局变量 全局调用
graph = None


# @asynccontextmanager 装饰器用于创建一个异步上下文管理器，它允许你在 yield 之前和之后执行特定的代码块，分别表示启动和关闭时的操作
@asynccontextmanager
async def lifespan(app: FastAPI):
 
    """
    启动时执行
    申明引用全局变量，在函数中被初始化，并在整个应用中使用
    函数在应用启动时执行一些初始化操作，如加载上下文数据、以及初始化问题生成器
    函数在应用关闭时执行一些清理操作
    """
    global graph, connection_pool
    # 启动时执行
    try:
        logger.info("正在初始化模型、定义 Graph...")
        connection_pool = None
        # 初始化 LLM
        llm = get_chat_llm()
        embedding = get_embedding()
        checkpointer, saver_type, connection_pool = get_graph_saver(connection_pool=connection_pool)
        logger.info("checkpointer 存储方式：" + saver_type);
        in_store, state_store_type, connection_pool = get_graph_store(embedding, connection_pool=connection_pool)

        logger.info("graph_store 存储方式：" + state_store_type);
 
        # # 注册chart 助手
        globals.register_assistant(llm, embedding, checkpointer, in_store)
        globals.connection_pool = connection_pool

        logger.info("初始化完成")
    except Exception as e:
        logger.error(f"初始化过程中出错: {str(e)}")
        raise

    yield  # 应用运行期间

    # 关闭时执行
    logger.info("正在关闭...")
    if connection_pool:
        connection_pool.close()  # 关闭连接池
        logger.info("数据库连接池已关闭")


# lifespan参数用于在应用程序生命周期的开始和结束时执行一些初始化或清理工作
app = FastAPI(lifespan=lifespan)
app.include_router(chat_router, prefix="/api/v1", tags=["chat"])
app.include_router(common_router, prefix="/api/v1", tags=["assistant"])

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Chatbot API is running"}



if __name__ == "__main__":
    import uvicorn
    logger.info(f"=============================================================")
    logger.info(f"======= 启动服务，端口号: {PORT}")
    logger.info(f"======= API文档地址: http://0.0.0.0:{PORT}/docs")
    logger.info(f"=============================================================")
    
    uvicorn.run(app, host="0.0.0.0", port=PORT)



