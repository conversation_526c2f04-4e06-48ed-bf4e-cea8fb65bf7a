import uuid
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from app.llms.llm import get_chat_llm, get_embedding, get_reasoner_llm
from langchain_core.messages import AIMessage, HumanMessage
from psycopg import Connection
from langgraph.checkpoint.postgres import PostgresSaver

class State(TypedDict):
    messages: Annotated[list, add_messages]

def chatbot(state: State):
    return {"messages": [llm.invoke(state["messages"])]}

DB_URI = "postgresql://postgres:postgres798@localhost:54322/postgres?sslmode=disable"
llm = get_chat_llm()

conn = Connection.connect(DB_URI)
checkpointer = PostgresSaver(conn)
checkpointer.setup()

graph_builder = StateGraph(State)
graph_builder.add_node("chatbot", chatbot)
graph_builder.add_edge(START, "chatbot")
graph_builder.add_edge("chatbot", END)

graph = graph_builder.compile(checkpointer=checkpointer)

def stream_graph_updates(user_input: str, config: dict):
    events = graph.stream({"messages": [{"role": "user", "content": user_input}]}, config, stream_mode="values")
    for event in events:
        last_event = event
    print("AI: ", last_event["messages"][-1].content)

if __name__ == "__main__":
    config = {"configurable": {"thread_id": uuid.uuid4().hex}}

    while True:
        user_input = input("User: ")
        if user_input.lower() in ["exit", "quit"]:
            break
        stream_graph_updates(user_input, config)

    print("\nHistory: ")
    for message in checkpointer.get(config)["channel_values"]["messages"]:
        if isinstance(message, AIMessage):
            prefix = "AI"
        else:
            prefix = "User"
        print(f"{prefix}: {message.content}")

    conn.close()
