"""
数据预处理工具集合
"""
from langchain.tools import tool
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns

from typing import Dict, TypedDict, List

@tool
def load_data(file_path: str, file_type: str = "csv") -> pd.DataFrame:
    """加载数据文件，支持csv、excel、json格式"""
    if file_type == "csv":
        return pd.read_csv(file_path)
    elif file_type == "excel":
        return pd.read_excel(file_path)
    elif file_type == "json":
        return pd.read_json(file_path)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")

@tool
def detect_data_types(df: pd.DataFrame) -> Dict:
    """检测数据集中各列的数据类型"""
    return {
        "numeric": df.select_dtypes(include=['number']).columns.tolist(),
        "categorical": df.select_dtypes(include=['object', 'category']).columns.tolist(),
        "datetime": df.select_dtypes(include=['datetime']).columns.tolist(),
        "boolean": df.select_dtypes(include=['bool']).columns.tolist()
    }

@tool
def handle_missing_values(df: pd.DataFrame, strategy: str = "mean", columns: List[str] = None) -> pd.DataFrame:
    """处理缺失值"""
    df_copy = df.copy()
    if columns is None:
        columns = df_copy.columns
        
    for col in columns:
        if df_copy[col].isnull().any():
            if strategy == "mean":
                df_copy[col].fillna(df_copy[col].mean(), inplace=True)
            elif strategy == "median":
                df_copy[col].fillna(df_copy[col].median(), inplace=True)
            elif strategy == "mode":
                df_copy[col].fillna(df_copy[col].mode()[0], inplace=True)
            elif strategy == "drop":
                df_copy.dropna(subset=[col], inplace=True)
            elif strategy == "interpolate":
                df_copy[col].interpolate(inplace=True)
    
    return df_copy

@tool
def detect_outliers(df: pd.DataFrame, method: str = "iqr", columns: List[str] = None) -> Dict:
    """检测异常值"""
    outliers = {}
    if columns is None:
        columns = df.select_dtypes(include=['number']).columns
        
    for col in columns:
        if method == "iqr":
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers[col] = df[(df[col] < lower_bound) | (df[col] > upper_bound)].index.tolist()
        elif method == "zscore":
            threshold = 3
            z_scores = (df[col] - df[col].mean()) / df[col].std()
            outliers[col] = df[abs(z_scores) > threshold].index.tolist()
    
    return outliers

@tool
def handle_outliers(df: pd.DataFrame, strategy: str = "remove", outliers_dict: Dict = None) -> pd.DataFrame:
    """处理异常值"""
    if outliers_dict is None:
        return df
        
    df_copy = df.copy()
    if strategy == "remove":
        outlier_indices = []
        for indices in outliers_dict.values():
            outlier_indices.extend(indices)
        df_copy = df_copy.drop(index=set(outlier_indices))
    elif strategy == "cap":
        for col, indices in outliers_dict.items():
            if not indices:
                continue
            Q1 = df_copy[col].quantile(0.25)
            Q3 = df_copy[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            df_copy.loc[df_copy[col] < lower_bound, col] = lower_bound
            df_copy.loc[df_copy[col] > upper_bound, col] = upper_bound
    
    return df_copy

@tool
def encode_categorical(df: pd.DataFrame, method: str = "onehot", columns: List[str] = None) -> pd.DataFrame:
    """分类变量编码"""
    df_copy = df.copy()
    if columns is None:
        columns = df_copy.select_dtypes(include=['object', 'category']).columns
        
    for col in columns:
        if method == "onehot":
            dummies = pd.get_dummies(df_copy[col], prefix=col)
            df_copy = pd.concat([df_copy, dummies], axis=1)
            df_copy.drop(col, axis=1, inplace=True)
        elif method == "label":
            df_copy[col] = df_copy[col].astype('category').cat.codes
    
    return df_copy

@tool
def normalize_data(df: pd.DataFrame, method: str = "standard", columns: List[str] = None) -> pd.DataFrame:
    """数据标准化/归一化"""
    df_copy = df.copy()
    if columns is None:
        columns = df_copy.select_dtypes(include=['number']).columns
        
    for col in columns:
        if method == "standard":
            scaler = StandardScaler()
            df_copy[col] = scaler.fit_transform(df_copy[[col]])
        elif method == "minmax":
            scaler = MinMaxScaler()
            df_copy[col] = scaler.fit_transform(df_copy[[col]])
    
    return df_copy

@tool
def split_dataset(df: pd.DataFrame, test_size: float = 0.2, val_size: float = 0.1, random_state: int = 42) -> Dict:
    """划分训练集、验证集和测试集"""
    train_val, test = train_test_split(df, test_size=test_size, random_state=random_state)
    relative_val_size = val_size / (1 - test_size)
    train, val = train_test_split(train_val, test_size=relative_val_size, random_state=random_state)
    
    return {
        "train": train,
        "val": val,
        "test": test
    }

@tool
def generate_report(df: pd.DataFrame, file_path: str = "data_report.html") -> str:
    """生成数据报告"""
    import pandas_profiling
    # minimal和default，其中minimal适用于大数据集（只算default的一部分），default是默认配置。
    profile = pandas_profiling.ProfileReport(df, 
                                             title = 'Pandas Profiling Report'
                                             html={"style": {"full_width": True}}, 
                                             minimal = True)
    profile.to_file(file_path)
    return f"Data report generated at {file_path}"

@tool
def visualize_data(df: pd.DataFrame, plot_type: str = "histogram", columns: List[str] = None, save_path: str = None) -> str:
    """数据可视化"""
    if columns is None:
        columns = df.columns
        
    for col in columns:
        plt.figure()
        if plot_type == "histogram":
            sns.histplot(df[col])
        elif plot_type == "boxplot":
            sns.boxplot(y=df[col])
        elif plot_type == "scatter" and len(columns) >= 2:
            sns.scatterplot(x=df[columns[0]], y=df[columns[1]])
            
        plt.title(f"{plot_type} of {col}")
        if save_path:
            plt.savefig(f"{save_path}/{col}_{plot_type}.png")
        plt.close()
    
    return f"Visualizations generated for {columns}"

@tool
def save_processed_data(df: pd.DataFrame, file_path: str, file_type: str = "csv") -> str:
    """保存处理后的数据"""
    if file_type == "csv":
        df.to_csv(file_path, index=False)
    elif file_type == "excel":
        df.to_excel(file_path, index=False)
    elif file_type == "json":
        df.to_json(file_path, orient="records")
    else:
        raise ValueError(f"Unsupported file type: {file_type}")
    
    return f"Data saved to {file_path} as {file_type}"