

import os
import logging
from dotenv import load_dotenv

# 加载 .env 文件（默认从当前目录查找）
load_dotenv()  
logger = logging.getLogger(__name__)


def get_graph_saver(connection_pool = None):
    """
    获取图数据库的连接字符串
    :return: 连接字符串
    """
    saver_type = os.getenv("MEMORY_TYPE", "memory")
    if saver_type == "memory":
        from langgraph.checkpoint.memory import MemorySaver
        checkpointer = MemorySaver()
    
    elif saver_type == "redis":
        from redis import Redis
        from langgraph.checkpoint.redis import RedisSaver
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        redis_client = Redis.from_url(redis_url)
        checkpointer = RedisSaver(redis_client==redis_client)
        # 创建 Redis 索引
        checkpointer.setup()
    elif saver_type == "postgres":
        from langgraph.checkpoint.postgres import PostgresSaver
        from psycopg_pool import ConnectionPool

        if connection_pool is None:
            connection_pool = get_pg_connection_pool()

        if not isinstance(connection_pool, ConnectionPool) :
            raise ValueError("connection_pool must be a ConnectionPool instance")

        # 短期记忆 初始化checkpointer
        checkpointer = PostgresSaver(connection_pool)
        checkpointer.setup()


    return checkpointer, saver_type, connection_pool



def get_graph_store(embedding, connection_pool = None):
    """
    获取图数据库的连接字符串
    :return: 连接字符串
    """
    state_store_type = os.getenv("STATE_TYPE", "memory")
    if state_store_type == "memory":
        from langgraph.store.memory import InMemoryStore
        in_store = InMemoryStore(
            index={
                "dims": 1536,
                "embed": embedding
            }
        )
    
    elif state_store_type == "redis":
        from langgraph.store.redis import RedisStore
        in_store = RedisStore(
            index={
                "dims": 1536,
                "embed": embedding
            }
        )
        in_store.setup()
    elif state_store_type == "postgres":
        from langgraph.store.postgres import PostgresStore
        from psycopg_pool import ConnectionPool
        if connection_pool is None:
            connection_pool = get_pg_connection_pool()

        if not isinstance(connection_pool, ConnectionPool) :
            raise ValueError("connection_pool must be a ConnectionPool instance")

        # 长期记忆 初始化PostgresStore
        in_store = PostgresStore(
            connection_pool,
            index={
                "dims": 1536,
                "embed": embedding
            }
        )
        in_store.setup()


    return in_store, state_store_type, connection_pool


def get_pg_connection_pool() -> str:

    from psycopg_pool import ConnectionPool
    # 创建数据库连接池
    DB_URI = os.getenv("PG_DB_URI", "postgresql://postgres:postgres8@localhost:5432/postgres?sslmode=disable") 
    connection_kwargs = {
        "autocommit": True,
        "prepare_threshold": 0,
    }
    connection_pool = ConnectionPool(
        conninfo=DB_URI,
        max_size=20,
        kwargs=connection_kwargs,
    )
    connection_pool.open()  # 显式打开连接池

    logger.info("数据库连接池初始化成功")

    return connection_pool