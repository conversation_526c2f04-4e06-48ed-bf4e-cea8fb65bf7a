import asyncio
from app.assistants.chat_assistant import LangGraphChatAssistant
from dotenv import load_dotenv

# 加载 .env 文件（默认从当前目录查找）
load_dotenv()  

async def test_chat():
    assistant = LangGraphChatAssistant()
    conversation_id = "test_cli"
    
    print("Starting CLI chat with DeepSeek. Type 'exit' to quit.")
    
    while True:
        user_input = input("You: ")
        if user_input.lower() in ["exit", "quit"]:
            break
        
        print("Assistant: ", end="", flush=True)
        full_response = ""
        async for chunk in assistant.chat(user_input, conversation_id=conversation_id):
            print(chunk, end="", flush=True)
            full_response += chunk
        print()
        
        # 显示对话状态
        state = await assistant.get_conversation_state(conversation_id)
        print(f"\n[Conversation state: {state['state']}]")
        
        # 每5条消息显示一次摘要
        history = await assistant.get_history(conversation_id)
        if len(history) % 5 == 0:
            summary = await assistant.generate_summary(conversation_id)
            print(f"\n[Conversation summary]: {summary}\n")

if __name__ == "__main__":
    asyncio.run(test_chat())