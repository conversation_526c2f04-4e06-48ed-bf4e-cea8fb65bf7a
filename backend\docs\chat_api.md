## chat_api 对话接口文档


这个 `ChatCompletionRequest` 类是一个数据模型（继承自 `pydantic.BaseModel`），用于定义聊天补全（Chat Completion）API 的请求参数。以下是各个参数的解释：

---

### **参数说明**

| 参数名 | 类型 | 是否必填 | 默认值 | 说明 |
|--------|------|----------|--------|------|
| `messages` | `List[Message]` | **是** | - | 聊天消息列表，包含用户输入和AI回复的历史记录 |
| `stream` | `Optional[bool]` | 否 | `False` | 是否启用流式响应（Streaming），用于逐块返回AI回复 |
| `userId` | `Optional[str]` | 否 | `None` | 用户唯一标识，可用于多用户场景下的会话隔离 |
| `conversationId` | `Optional[str]` | 否 | `None` | 会话唯一标识，用于关联同一对话的多次请求 |
| `assistantId` | `str` | 否 | `"chat"` | 助手ID，用于区分不同的AI服务或角色 |

---

### **详细说明**

1. **`messages`**  
   - **类型**: `List[Message]`（通常 `Message` 是一个包含 `role` 和 `content` 的对象）  
   - **作用**: 定义聊天的上下文，格式一般为：

     ```python
     [
         {"role": "user", "content": "你好！"},
         {"role": "assistant", "content": "你好，有什么可以帮您？"}
     ]
     ```

   - 每条消息需包含：
     - `role`: 发送者角色（如 `user`/`assistant`/`system`）。  
     - `content`: 消息内容。

2. **`stream`**  
   - 如果设为 `True`，API 会以流式（Server-Sent Events, SSE）返回响应，适用于实时显示AI回复的场景（如逐字输出）。  
   - 默认 `False` 表示一次性返回完整响应。

3. **`userId` 和 `conversationId`**  
   - 用于多用户或多会话管理：
     - `userId`: 区分不同用户（例如用户登录后的ID）。  
     - `conversationId`: 区分同一用户的多次对话（例如保存聊天历史时关联到某次对话）。  
   - 如果未提供，系统可能自动生成或忽略。

4. **`assistantId`**  
   - 标识调用的AI服务类型，例如：
     - `"chat"`: 通用聊天助手  
     - `"code"`: 代码生成专用  
     - 默认值 `"chat"` 表示通用对话场景。

---

### **使用示例**

```python
request = ChatCompletionRequest(
    messages=[
        {"role": "user", "content": "Python怎么打印Hello World？"}
    ],
    stream=True,
    userId="123",
    conversationId="abc-xyz",
    assistantId="code"
)
```

---

### **典型应用场景**

1. **普通聊天**  
   - 只需提供 `messages`，其他参数用默认值。
2. **多轮对话管理**  
   - 通过 `conversationId` 关联同一对话的多次请求。
3. **流式输出**  
   - 设置 `stream=True` 实现类似ChatGPT的逐字输出效果。
4. **多AI服务切换**  
   - 通过 `assistantId` 选择不同的AI模型（如聊天/代码/翻译）。

如果有其他字段或更复杂的需求，可以进一步扩展该模型。