[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "research-assistant"
version = "0.0.1"
description = "research-assistant project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "arxiv>=2.2.0",
    "asyncpg>=0.30.0",
    "fastapi>=0.116.1",
    "gradio>=5.38.0",
    "jose>=1.0.0",
    "langchain-community>=0.3.27",
    "langchain-openai>=0.3.28",
    "langchain-tavily>=0.2.10",
    "langgraph>=0.5.3",
    "langgraph-checkpoint-duckdb>=2.0.2",
    "langgraph-checkpoint-mysql>=2.0.15",
    "langgraph-checkpoint-postgres>=2.0.23",
    "langgraph-checkpoint-redis>=0.0.8",
    "motor>=3.7.1",
    "openrouter>=1.0",
    "paginate>=0.5.7",
    "pandas-profiling>=3.6.6",
    "psycopg-binary>=3.2.9",
    "psycopg-pool>=3.2.6",
    "python-dotenv>=1.1.1",
    "redis>=6.2.0",
    "scikit-learn>=1.7.1",
    "starlette-context>=0.4.0",
    "uvicorn>=0.35.0",
]


[tool.hatch.build.targets.wheel]
packages = ["app"]



[[tool.poetry.source]]
name = "mirrors"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "default"

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true
 
[tool.uv.pip]
index-url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
