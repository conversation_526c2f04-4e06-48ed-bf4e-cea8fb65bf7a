

## 通过docker快速构建本地开发环境


开发环境依赖：
1. postgres + vector
2. redis
3. 


### 单独构建postgrsql image 镜像


#### 启动docker compose

```bash
# 启动docker compose
docker-compose -f docker-compose-dev.yml up -d

# 停止服务
docker-compose -f docker-compose-dev.yml stop

# 停止并删除所有容器
docker-compose -f docker-compose-dev.yml down

```



#### 确认pg是否安装vector扩展

进入postgres容器

```bash 
docker exec -it pg psql -U postgres -d postgres
```

判断vector工具是否安装成功

```bash
CREATE EXTENSION IF NOT EXISTS vector;
```

### 常见问题


- 1.macos创建pg数据库失败

MacOS (使用 Homebrew): brew install postgresql
最后，再安装相关依赖包
uv add langgraph-checkpoint-postgres
uv add psycopg psycopg-pool


window连接pg 本地可能的问题
