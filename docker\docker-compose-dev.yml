# 数据库的默认账号和密码仅首次运行时设置有效
# 如果修改了账号密码，记得改数据库和项目连接参数，别只改一处~
# 该配置文件只是给快速启动，测试使用。正式使用，记得务必修改账号密码，以及调整合适的知识库参数，共享内存等。
# 如何无法访问 dockerhub 和 git，可以用阿里云（阿里云没有arm包）

version: '1.0'
services:
  # Vector DB
  pg:
    # image: pgvector/pgvector:0.8.0-pg15 # docker hub
    image: registry.cn-hangzhou.aliyuncs.com/fastgpt/pgvector:v0.8.0-pg15 # 阿里云
    container_name: pg
    restart: always
    ports: # 生产环境建议不要暴露
      - 54322:5432
    networks:
      - fastgpt
    environment:
      # 这里的配置只有首次运行生效。修改后，重启镜像是不会生效的。需要把持久化数据删除再重启，才有效果
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres798
      - POSTGRES_DB=postgres
    volumes:
      - ./pg/data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD', 'pg_isready', '-U', 'postgres', '-d', 'postgres']
      interval: 5s
      timeout: 5s
      retries: 10
    command: ["postgres", "-c", "max_connections=200"]  # 自定义配置


  redis:
    # image: redis:7.2-alpine
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/redis:7.2-alpine
    container_name: redis
    networks:
      - fastgpt
    restart: always
    ports: # 生产环境建议不要暴露
      - 63791:6379
    command: |
      redis-server --requirepass mypassword --loglevel warning --maxclients 10000 --appendonly yes --save 60 10 --maxmemory 4gb --maxmemory-policy noeviction
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', 'mypassword', 'ping']
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 30s
    volumes:
      - ./redis/data:/data
 
networks:
  fastgpt:
