import request from '@/utils/request'

/**
 * 助手相关的API接口
 */
export const assistantAPI = {
  /**
   * 获取所有可用的助手列表
   * @returns {Promise<Array>} 助手列表
   */
  async getAssistants() {
    try {
      const response = await request.get('/assistants')
      return response.data
    } catch (error) {
      console.error('Failed to load assistants:', error)
      throw error
    }
  },

  /**
   * 获取指定助手的详细信息
   * @param {string} assistantId - 助手ID
   * @returns {Promise<Object>} 助手详细信息
   */
  async getAssistantDetails(assistantId) {
    if (!assistantId) {
      throw new Error('Assistant ID is required')
    }
    
    try {
      const assistants = await this.getAssistants()
      const assistant = assistants.find(a => a.id === assistantId)
      if (!assistant) {
        throw new Error('Assistant not found')
      }
      return assistant
    } catch (error) {
      console.error('Failed to load assistant details:', error)
      throw error
    }
  }
}

export default assistantAPI