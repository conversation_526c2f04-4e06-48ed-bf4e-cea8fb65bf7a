import request from '@/utils/request'
import { getBaseAPI } from '@/utils/env'

/**
 * 聊天相关的API接口
 */
export const chatAPI = {
  /**
   * 发送流式聊天消息
   * @param {string} message - 用户消息
   * @param {Function} onMessage - 接收消息回调
   * @param {Function} onError - 错误回调
   * @param {Function} onComplete - 完成回调
   * @param {Function} onSessionId - 接收会话ID回调
   * @param {string} conversationId - 对话ID（可选）
   * @param {string} assistantId - 助手ID（新对话时必需）
   * @param {boolean} deepThinking - 是否启用深度思考模式
   * @returns {Function} 停止流式请求的函数
   */
  sendStreamMessage(message, userId, onMessage, onError, onComplete, onSessionId, conversationId = null, assistantId = null, deepThinking = false) {
    const controller = new AbortController()
    let currentSessionId = null
    
    // 立即返回取消函数，确保它总是可用
    const cancelFunction = () => {
      // 1. 立即中止前端请求
      controller.abort();
      
      // 2. 如果有会话ID，通知后端停止推理
      if (currentSessionId) {
        chatAPI.stopChat(currentSessionId)
        .then(() => {
          // 成功停止，不需要额外提示，用户已经看到"已取消生成"
        })
        .catch(error => {
          console.warn('Failed to notify backend to stop:', error);
        });
      }
    };
    
    // 异步执行请求
    // 注意：流式请求必须使用fetch，因为axios不支持SSE
    (async () => {
      try {
        const userMessage = [{
          role: 'user',
          content: message,
        }];

        if (!conversationId) {
          // 生成一个随机的conversationId
          conversationId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        }

        const response = await fetch(`${getBaseAPI()}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            stream: true,
            messages: userMessage,
            userId: userId, 
            conversationId: conversationId,
            assistantId: assistantId,
            deepThinking: deepThinking
          }),
          signal: controller.signal
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()

        while (true) {
          const { done, value } = await reader.read()
          
          if (done) {
            onComplete?.()
            break
          }

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('detail:')) {
              onError?.(data.error)
              return

            }
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))
                
                currentSessionId = data.id
                const status_code = data.status_code
                const chunk = data.choices[0].delta.content ? data.choices[0].delta.content : ''
                const isStop = data.choices[0].finish_reason
                if (isStop && isStop === 'stop') {
                  onComplete?.()
                  return
                }
                onSessionId?.(currentSessionId, data.conversationId)
                onMessage?.(chunk)

              } catch (e) {
                console.warn('Failed to parse SSE data:', line)
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          onError?.(error.message || '网络请求失败')
        }
      }
    })();

    // 返回取消函数
    return cancelFunction;
  },

  /**
   * 停止聊天会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 停止结果
   */
  async stopChat(sessionId) {
    try {
      return await request.post('/chat/stop', { session_id: sessionId })
    } catch (error) {
      console.error('Stop chat failed:', error)
      throw error
    }
  }
}

export default chatAPI