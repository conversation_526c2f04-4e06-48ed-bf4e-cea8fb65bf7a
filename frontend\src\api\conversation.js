import mockAPI from './mockData'
import request from '@/utils/request'

/**
 * 会话相关的API接口
 */
export const conversationAPI = {
  /**
   * 获取指定助手的会话列表（支持分页）
   * @param {string} assistantId - 助手ID
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @returns {Promise<Object>} 响应数据
   */
  async getConversations(assistantId, userId, page = 1, limit = 20) {
    if (!assistantId) {
      throw new Error('Assistant ID is required')
    }
    
    try {
      const response = await request.get(`/assistants/${assistantId}/conversations`, {
        params: { page, limit, userId }
      })
      return response.data
    } catch (error) {
      console.error('Failed to load conversations:', error)
      throw error
    }
  },

  /**
   * 获取指定会话的消息历史
   * @param {string} conversationId - 会话ID
   * @returns {Promise<Array>} 消息列表
   */
  async getMessages(conversationId, assistantId) {
    if (!conversationId || conversationId.startsWith('temp_')) {
      return []
    }
    
    try {
      const response = await request.get(`/assistants/completions/history?assistantId=${assistantId}&&threadId=${conversationId}`)
      return response.data
    } catch (error) {
      console.error('Failed to load messages:', error)
      throw error
    }
  },

  /**
   * 创建新会话
   * @param {string} assistantId - 助手ID
   * @param {string} userId - 用户ID
   * @param {string} title - 会话标题
   * @returns {Promise<Object>} 创建的会话数据
   */
  async createConversation(assistantId, userId, title = '新对话') {
    if (!assistantId) {
      throw new Error('Assistant ID is required')
    }
    if (!userId) {
      throw new Error('User ID is required')
    }
    
    try {
      // 生成唯一的会话ID
      const conversationId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      
      const response = await request.post('/assistants/conversation', {
        assistantId: assistantId,
        userId: userId,
        conversationId: conversationId,
        name: title
      })
      
      // 返回包含conversationId的完整数据，转换为驼峰命名
      return {
        conversationId: conversationId,
        assistantId: assistantId,
        userId: userId,
        name: title,
        createdAt: new Date().toISOString()
      }
    } catch (error) {
      console.error('Failed to create conversation:', error)
      throw error
    }
  },

  /**
   * 发送消息
   * @param {string} conversationId - 会话ID
   * @param {string} content - 消息内容
   * @returns {Promise<Object>} 消息响应数据
   */
  async sendMessage(conversationId, content) {
    if (!conversationId || !content) {
      throw new Error('Conversation ID and content are required')
    }
    
    try {
      const response = await mockAPI.sendMessage(conversationId, content)
      if (!response.success) {
        throw new Error('Failed to send message')
      }
      return response.data
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  },

  /**
   * 更新会话标题
   * @param {string} conversationId - 会话ID
   * @param {string} title - 新标题
   * @returns {Promise<void>}
   */
  async updateConversationTitle(conversationId, title) {
    if (!conversationId || !title) {
      throw new Error('Conversation ID and title are required')
    }
    
    try {
      const response = await mockAPI.updateConversationTitle(conversationId, title)
      if (!response.success) {
        throw new Error('Failed to update conversation title')
      }
      return response.data
    } catch (error) {
      console.error('Failed to update conversation title:', error)
      throw error
    }
  },

  /**
   * 删除会话
   * @param {string} conversationId - 会话ID
   * @returns {Promise<void>}
   */
  async deleteConversation(conversationId) {
    if (!conversationId) {
      throw new Error('Conversation ID is required')
    }
    
    try {
      const response = await mockAPI.deleteConversation(conversationId)
      if (!response.success) {
        throw new Error('Failed to delete conversation')
      }
      return response.data
    } catch (error) {
      console.error('Failed to delete conversation:', error)
      throw error
    }
  }
}

export default conversationAPI