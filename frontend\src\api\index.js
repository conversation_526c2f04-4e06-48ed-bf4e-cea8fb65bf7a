// API 统一入口文件
export { default as conversationAPI } from './conversation'
export { default as assistantAPI } from './assistant'
export { default as mockAPI } from './mockData'
export { default as chatAPI } from './chat'

// 导出具名API对象
import conversationAPI from './conversation'
import assistantAPI from './assistant'
import chatAPI from './chat'

export const api = {
  conversation: conversationAPI,
  assistant: assistantAP<PERSON>,
  chat: chatAPI
}

export default api