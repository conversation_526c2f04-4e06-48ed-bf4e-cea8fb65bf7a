// Mock数据和API服务

// 助手数据
export const mockAssistants = [
  {
    id: 'assistant_1',
    label: '医学研究助手',
    description: '专业的医学研究和文献分析助手',
    avatar: '🔬',
    specialty: 'medical'
  },
  {
    id: 'assistant_2', 
    label: '数据分析助手',
    description: '专注于数据分析和统计建模',
    avatar: '📊',
    specialty: 'data'
  },
  {
    id: 'assistant_3',
    label: '文献综述助手', 
    description: '帮助进行文献搜索和综述撰写',
    avatar: '📚',
    specialty: 'literature'
  },
  {
    id: 'assistant_4',
    label: '实验设计助手',
    description: '协助设计和优化科研实验方案',
    avatar: '🧪',
    specialty: 'experiment'
  }
]

// 为每个助手生成会话数据
const generateConversationsForAssistant = (assistantId, assistantLabel) => {
  const conversations = []
  const now = Date.now()
  
  // 今天的会话 (8-12个)
  const todayCount = Math.floor(Math.random() * 5) + 8
  // const todayCount = 1
  for (let i = 1; i <= todayCount; i++) {
    conversations.push({
      id: `${assistantId}_today_${i}`,
      assistantId,
      label: `${assistantLabel} - 今天的会话${i}`,
      group: 'today',
      createdAt: new Date(now - i * 30 * 60 * 1000).toISOString(),
      lastMessageAt: new Date(now - i * 30 * 60 * 1000).toISOString()
    })
  }
  
  // 昨天的会话 (6-10个)
  const yesterdayCount = Math.floor(Math.random() * 5) + 6
  // const yesterdayCount = 1
  for (let i = 1; i <= yesterdayCount; i++) {
    conversations.push({
      id: `${assistantId}_yesterday_${i}`,
      assistantId,
      label: `${assistantLabel} - 昨天的会话${i}`,
      group: 'yesterday', 
      createdAt: new Date(now - 24 * 60 * 60 * 1000 - i * 45 * 60 * 1000).toISOString(),
      lastMessageAt: new Date(now - 24 * 60 * 60 * 1000 - i * 45 * 60 * 1000).toISOString()
    })
  }
  
  // 本周的会话 (4-8个)
  const thisWeekCount = Math.floor(Math.random() * 5) + 4
  // const thisWeekCount = 1
  for (let i = 1; i <= thisWeekCount; i++) {
    conversations.push({
      id: `${assistantId}_thisWeek_${i}`,
      assistantId,
      label: `${assistantLabel} - 本周的会话${i}`,
      group: 'thisWeek',
      createdAt: new Date(now - (2 + i) * 24 * 60 * 60 * 1000).toISOString(),
      lastMessageAt: new Date(now - (2 + i) * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  
  // 本月的会话 (3-6个)
  const thisMonthCount = Math.floor(Math.random() * 4) + 3
  // const thisMonthCount = 1
  for (let i = 1; i <= thisMonthCount; i++) {
    conversations.push({
      id: `${assistantId}_thisMonth_${i}`,
      assistantId,
      label: `${assistantLabel} - 本月的会话${i}`,
      group: 'thisMonth',
      createdAt: new Date(now - (10 + i * 2) * 24 * 60 * 60 * 1000).toISOString(),
      lastMessageAt: new Date(now - (10 + i * 2) * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  
  // 更早的会话 (2-4个)
  const olderCount = Math.floor(Math.random() * 3) + 2
  // const olderCount = 1
  for (let i = 1; i <= olderCount; i++) {
    conversations.push({
      id: `${assistantId}_older_${i}`,
      assistantId,
      label: `${assistantLabel} - 更早的会话${i}`,
      group: 'older',
      createdAt: new Date(now - (30 + i * 10) * 24 * 60 * 60 * 1000).toISOString(),
      lastMessageAt: new Date(now - (30 + i * 10) * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  
  // 未分组的会话 (1-3个)
  const ungroupedCount = Math.floor(Math.random() * 3) + 1
  // const ungroupedCount = 1
  for (let i = 1; i <= ungroupedCount; i++) {
    conversations.push({
      id: `${assistantId}_ungrouped_${i}`,
      assistantId,
      label: `${assistantLabel} - 未分组的会话${i}`,
      createdAt: new Date(now - (60 + i * 15) * 24 * 60 * 60 * 1000).toISOString(),
      lastMessageAt: new Date(now - (60 + i * 15) * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  
  return conversations
}

// 生成所有助手的会话数据
export const mockConversations = {}
mockAssistants.forEach(assistant => {
  mockConversations[assistant.id] = generateConversationsForAssistant(assistant.id, assistant.label)
})

// 为每个会话生成历史消息
const generateMessagesForConversation = (conversationId, conversationLabel) => {
  const messages = []
  const messageCount = Math.floor(Math.random() * 10) + 5 // 5-15条消息
  
  for (let i = 1; i <= messageCount; i++) {
    // 用户消息
    messages.push({
      id: `${conversationId}_user_${i}`,
      conversationId,
      role: 'user',
      content: `我是会话 ${conversationId} 的用户消息${i}`,
      timestamp: new Date(Date.now() - (messageCount - i + 1) * 5 * 60 * 1000).toISOString()
    })
    
    // 助手回复 (不是每条用户消息都有回复)
    if (i <= messageCount - 1 || Math.random() > 0.3) {
      messages.push({
        id: `${conversationId}_assistant_${i}`,
        conversationId,
        role: 'assistant', 
        content: `我是会话 ${conversationId} 的助手回复${i}，针对您的问题，我的回答是...`,
        timestamp: new Date(Date.now() - (messageCount - i) * 5 * 60 * 1000 - 30 * 1000).toISOString()
      })
    }
  }
  
  return messages
}

// 生成所有会话的消息数据
export const mockMessages = {}
Object.values(mockConversations).flat().forEach(conversation => {
  mockMessages[conversation.id] = generateMessagesForConversation(conversation.id, conversation.label)
})

// Mock API服务
export const mockAPI = {
  // 获取助手列表
  getAssistants() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: mockAssistants
        })
      }, 300 + Math.random() * 700) // 300-1000ms 随机延迟
    })
  },
  
  // 获取指定助手的会话列表
  getConversations(assistantId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const conversations = mockConversations[assistantId] || []
        resolve({
          success: true,
          data: conversations
        })
      }, 200 + Math.random() * 800) // 200-1000ms 随机延迟
    })
  },
  
  // 获取指定会话的消息历史
  getMessages(conversationId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const messages = mockMessages[conversationId] || []
        resolve({
          success: true,
          data: messages
        })
      }, 100 + Math.random() * 500) // 100-600ms 随机延迟
    })
  },
  
  // 创建新会话
  createConversation(assistantId, title) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newConversation = {
          id: `${assistantId}_new_${Date.now()}`,
          assistantId,
          label: title || '新会话',
          group: 'today',
          createdAt: new Date().toISOString(),
          lastMessageAt: new Date().toISOString()
        }
        
        // 添加到mock数据中
        if (!mockConversations[assistantId]) {
          mockConversations[assistantId] = []
        }
        mockConversations[assistantId].unshift(newConversation)
        mockMessages[newConversation.id] = []
        
        resolve({
          success: true,
          data: newConversation
        })
      }, 200 + Math.random() * 300) // 200-500ms 随机延迟
    })
  },
  
  // 发送消息
  sendMessage(conversationId, content) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const userMessage = {
          id: `${conversationId}_user_${Date.now()}`,
          conversationId,
          role: 'user',
          content,
          timestamp: new Date().toISOString()
        }
        
        const assistantMessage = {
          id: `${conversationId}_assistant_${Date.now()}`,
          conversationId,
          role: 'assistant',
          content: `我收到了您的消息: "${content}"，这是我的回复...`,
          timestamp: new Date(Date.now() + 1000).toISOString()
        }
        
        // 添加到mock数据中
        if (!mockMessages[conversationId]) {
          mockMessages[conversationId] = []
        }
        mockMessages[conversationId].push(userMessage, assistantMessage)
        
        resolve({
          success: true,
          data: {
            userMessage,
            assistantMessage
          }
        })
      }, 500 + Math.random() * 1500) // 500-2000ms 随机延迟，模拟AI回复时间
    })
  },
  
  // 更新会话标题
  updateConversationTitle(conversationId, title) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 在所有助手的会话中查找并更新
        let updated = false
        Object.keys(mockConversations).forEach(assistantId => {
          const conversation = mockConversations[assistantId].find(conv => conv.id === conversationId)
          if (conversation) {
            conversation.label = title
            updated = true
          }
        })
        
        resolve({
          success: updated,
          data: updated ? { conversationId, title } : null
        })
      }, 100 + Math.random() * 200) // 100-300ms 随机延迟
    })
  },
  
  // 删除会话
  deleteConversation(conversationId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 在所有助手的会话中查找并删除
        let deleted = false
        Object.keys(mockConversations).forEach(assistantId => {
          const index = mockConversations[assistantId].findIndex(conv => conv.id === conversationId)
          if (index !== -1) {
            mockConversations[assistantId].splice(index, 1)
            delete mockMessages[conversationId] // 同时删除相关消息
            deleted = true
          }
        })
        
        resolve({
          success: deleted,
          data: deleted ? { conversationId } : null
        })
      }, 100 + Math.random() * 200) // 100-300ms 随机延迟
    })
  }
}

export default mockAPI