import request from '@/utils/request.js'
export const userRegisterService = (registerForm)=>{
    return request.post('/register', {
        username: registerForm.username,
        password: registerForm.password,
        nickname: registerForm.nickname,
    });
}
export const userLoginService = (loginForm)=>{

    return request.post('/login', null, {
        params: {
            username: loginForm.username,
            password: loginForm.password
        }
    });
}
export const userInfService= ()=>{
    return request.get('/userInfo');
}


export const updatePasswordService=(rePasswordForm)=>{
    console.log(rePasswordForm.password)
    return request.post('/changePassword',null,{
        params:{
            password:rePasswordForm.password,
            rePassword:rePasswordForm.rePassword
        }
    })
}
export const updateNicknameService=(nickName)=>{
    return request.post('/changeNickName',null,{
        params:{
            nickName:nickName
        }
    })
}

export const  updateAvatarService=()=>{

}