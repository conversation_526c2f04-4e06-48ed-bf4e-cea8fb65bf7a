<template>
  <div class="assistant-selector-wrapper">
    <!-- 助手选择器触发器 -->
    <div v-if="assistantStore.selectedAssistant" class="current-assistant-header">
      <div class="assistant-selector">
        <span v-if="showLabel" class="assistant-label">当前助手:</span>
        <div class="assistant-trigger" :class="triggerClass" @click="showAssistantDrawer = true">
          <div class="current-assistant-info">
            <span class="assistant-avatar">{{ assistantStore.selectedAssistant.avatar || '🤖' }}</span>
            <span class="assistant-name">{{ assistantStore.selectedAssistant.label }}</span>
          </div>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 助手选择抽屉 -->
    <el-drawer
      v-model="showAssistantDrawer"
      direction="ltr"
      size="420px"
      :z-index="2000"
      :modal="true"
      :close-on-click-modal="true"
      :show-close="true"
      :with-header="false"
    >
      <div class="assistant-drawer-content">
        <div class="drawer-header">
          <div class="header-content">
            <h3>切换助手</h3>
            <p class="drawer-subtitle">选择最适合您需求的专业助手</p>
          </div>
          <el-button 
            class="close-btn" 
            circle 
            size="small" 
            @click="showAssistantDrawer = false"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        
        <div class="assistants-grid">
          <div
            v-for="assistant in assistantTypes"
            :key="assistant.id"
            class="assistant-card"
            :class="{ active: assistant.id === assistantStore.selectedAssistant?.id }"
            @click="handleAssistantCardClick(assistant)"
          >
            <div class="card-header">
              <div class="assistant-avatar-large">{{ assistant.avatar || '🤖' }}</div>
              <div class="assistant-meta">
                <h4 class="assistant-title">{{ assistant.label }}</h4>
                <span class="assistant-specialty">{{ assistant.specialty || '通用助手' }}</span>
              </div>
            </div>
            
            <p class="assistant-description">{{ assistant.description }}</p>
            
            <div v-if="assistant.capabilities && assistant.capabilities.length" class="capabilities">
              <span
                v-for="capability in assistant.capabilities.slice(0, 3)"
                :key="capability"
                class="capability-tag"
              >
                {{ capability }}
              </span>
              <span v-if="assistant.capabilities.length > 3" class="more-capabilities">
                +{{ assistant.capabilities.length - 3 }}
              </span>
            </div>
            
            <div class="card-footer">
              <el-icon v-if="assistant.id === assistantStore.selectedAssistant?.id" class="selected-icon">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ArrowDown, Check, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useAssistantStore } from '@/stores/assistant'
import { assistantAPI } from '@/api'

export default {
  name: 'AssistantSelector',
  components: {
    ArrowDown,
    Check,
    Close
  },
  props: {
    // 是否显示标签文本
    showLabel: {
      type: Boolean,
      default: false
    },
    // 自定义触发器样式类
    triggerClass: {
      type: String,
      default: ''
    }
  },
  emits: ['assistant-changed'],
  setup(props, { emit }) {
    const assistantStore = useAssistantStore()
    const showAssistantDrawer = ref(false)
    const assistantTypes = ref([])

    // 加载助手列表
    const loadAssistants = async () => {
      try {
        if (assistantStore.hasAssistantsList) {
          assistantTypes.value = assistantStore.assistantsList.map(assistant => ({
            key: assistant.id,
            label: assistant.label,
            description: assistant.description,
            avatar: assistant.avatar,
            specialty: assistant.specialty,
            capabilities: assistant.capabilities || [],
            tags: assistant.tags || [],
            id: assistant.id
          }))
        } else {
          const assistants = await assistantAPI.getAssistants()
          assistantStore.setAssistantsList(assistants)
          assistantTypes.value = assistants.map(assistant => ({
            key: assistant.id,
            label: assistant.label,
            description: assistant.description,
            avatar: assistant.avatar,
            specialty: assistant.specialty,
            capabilities: assistant.capabilities || [],
            tags: assistant.tags || [],
            id: assistant.id
          }))
        }
      } catch (error) {
        console.error('Failed to load assistants:', error)
        ElMessage.error('加载助手列表失败')
      }
    }

    // 助手卡片点击处理
    const handleAssistantCardClick = async (assistant) => {
      if (assistant.id !== assistantStore.selectedAssistant?.id) {
        assistantStore.setAssistant(assistant)
        showAssistantDrawer.value = false
        
        // 发射助手切换事件
        emit('assistant-changed', assistant)
      } else {
        showAssistantDrawer.value = false
      }
    }

    onMounted(() => {
      loadAssistants()
    })

    return {
      assistantStore,
      showAssistantDrawer,
      assistantTypes,
      loadAssistants,
      handleAssistantCardClick
    }
  }
}
</script>

<style lang="scss" scoped>
.assistant-selector-wrapper {
  width: 100%;
}

/* 助手选择器样式 */
.current-assistant-header {
  padding: 5px 0;
}

.assistant-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assistant-label {
  color: #606266;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.assistant-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.assistant-trigger:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.current-assistant-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.assistant-avatar {
  font-size: 14px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.assistant-name {
  color: #409eff;
  font-weight: 500;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-icon {
  color: #409eff;
  font-size: 12px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.assistant-trigger:hover .dropdown-icon {
  transform: translateY(1px);
}

/* 助手抽屉样式 */
.assistant-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 12px 16px 16px 20px;
  overflow: hidden;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 10px;

  .header-content {
    flex: 1;
    
    h3 {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .drawer-subtitle {
      margin: 0;
      font-size: 13px;
      color: #8e8e93;
      line-height: 1.4;
    }
  }

  .close-btn {
    background: #f5f5f5;
    border: none;
    color: #666;
    flex-shrink: 0;
    margin-left: 12px;

    &:hover {
      background: #e0e0e0;
      color: #333;
    }
  }
}

.assistants-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-top: 5px;
  padding-right: 12px;
  margin-right: -8px;
  
  
  .assistant-card {
    background: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    
    &:hover {
      border-color: #007aff;
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
      transform: translateY(-1px);
    }
    
    &.active {
      border-color: #007aff;
      background: #f0f8ff;
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
    }
  }
  
  .card-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
    
    .assistant-avatar-large {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      flex-shrink: 0;
    }
    
    .assistant-meta {
      flex: 1;
      min-width: 0;
      
      .assistant-title {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.3;
      }
      
      .assistant-specialty {
        font-size: 12px;
        color: #007aff;
        background: #e8f4fd;
        padding: 2px 8px;
        border-radius: 4px;
        display: inline-block;
      }
    }
  }
  
  .assistant-description {
    font-size: 13px;
    color: #666666;
    line-height: 1.4;
    margin: 0 0 12px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .capabilities {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 12px;
    
    .capability-tag {
      font-size: 11px;
      color: #666;
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 3px;
      white-space: nowrap;
    }
    
    .more-capabilities {
      font-size: 11px;
      color: #999;
      font-weight: 500;
    }
  }
  
  .card-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    
    .selected-icon {
      color: #007aff;
      font-size: 18px;
    }
  }
}
</style>