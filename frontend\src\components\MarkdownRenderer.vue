<template>
  <div class="markdown-renderer" :class="{ 'streaming': isStreaming, 'historical': !isStreaming }">
    <!-- 使用 XMarkdown 组件进行渲染 -->
    <XMarkdown 
      :markdown="displayContent"
      :class="markdownClass"
      :allow-html="true"
      :enable-latex="true"
      :enable-breaks="true"
      @copy="handleCopy"
    />
    
    <!-- 流式渲染时的光标指示器 -->
    <div 
      v-if="isStreaming && showCursor" 
      class="streaming-cursor"
      :class="{ 'typing': isTyping }"
    >
      <span class="cursor-blink">|</span>
    </div>
    
    <!-- 渲染完成指示器 -->
    <div v-if="!isStreaming && showCompleteIndicator" class="render-complete">
      <el-icon class="complete-icon"><Check /></el-icon>
      <span>渲染完成</span>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { Check } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { XMarkdown } from 'vue-element-plus-x'

export default {
  name: 'MarkdownRenderer',
  components: {
    Check,
    XMarkdown
  },
  props: {
    // 原始内容
    content: {
      type: String,
      default: ''
    },
    
    // 是否为流式渲染模式
    isStreaming: {
      type: Boolean,
      default: false
    },
    
    // 流式渲染速度控制
    streamingSpeed: {
      type: Number,
      default: 50 // 毫秒
    },
    
    // 主题配置
    theme: {
      type: String,
      default: 'github'
    },
    
    // 是否显示光标
    showCursor: {
      type: Boolean,
      default: true
    },
    
    // 是否显示完成指示器
    showCompleteIndicator: {
      type: Boolean,
      default: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },
    
    // XMarkdown 配置选项
    options: {
      type: Object,
      default: () => ({
        html: true,
        xhtmlOut: false,
        breaks: true,        // 保留换行，让纯文本显示更自然
        langPrefix: 'language-',
        linkify: true,       // 自动识别链接
        typographer: false,  // 关闭花哨排版，对纯文本更友好
        quotes: '""``'
      })
    }
  },
  
  emits: [
    'render-complete',
    'streaming-progress', 
    'code-copy',
    'link-click',
    'render-error'
  ],
  
  setup(props, { emit }) {
    const displayContent = ref('')
    const isTyping = ref(false)
    const renderTimer = ref(null)
    const currentIndex = ref(0)
    
    // 计算属性
    const markdownClass = computed(() => {
      return [
        'markdown-content',
        props.customClass,
        {
          'streaming-mode': props.isStreaming,
          'historical-mode': !props.isStreaming
        }
      ]
    })
    
    // XMarkdown 配置已直接在模板中设置
    
    // 流式渲染逻辑
    const startStreaming = () => {
      if (!props.isStreaming || !props.content) return
      
      currentIndex.value = 0
      displayContent.value = ''
      isTyping.value = true
      
      const streamContent = () => {
        if (currentIndex.value < props.content.length) {
          // 逐字符添加内容
          const nextChar = props.content[currentIndex.value]
          displayContent.value += nextChar
          currentIndex.value++
          
          // 发射进度事件
          emit('streaming-progress', {
            progress: (currentIndex.value / props.content.length) * 100,
            currentChar: nextChar,
            displayedContent: displayContent.value
          })
          
          // 智能速度控制 - 遇到标点符号稍微停顿
          const delay = ['.', '!', '?', '\n'].includes(nextChar) 
            ? props.streamingSpeed * 2 
            : props.streamingSpeed
            
          renderTimer.value = setTimeout(streamContent, delay)
        } else {
          // 渲染完成
          isTyping.value = false
          emit('render-complete', {
            content: displayContent.value,
            totalChars: currentIndex.value
          })
        }
      }
      
      streamContent()
    }
    
    // 停止流式渲染
    const stopStreaming = () => {
      if (renderTimer.value) {
        clearTimeout(renderTimer.value)
        renderTimer.value = null
      }
      isTyping.value = false
      displayContent.value = props.content
    }
    
    // 处理历史内容（非流式）
    const renderHistorical = () => {
      displayContent.value = props.content
      nextTick(() => {
        emit('render-complete', {
          content: displayContent.value,
          isHistorical: true
        })
      })
    }
    
    // 事件处理器
    const handleCopy = (event) => {
      ElMessage.success('代码已复制到剪贴板')
      emit('code-copy', event)
    }
    
    // 监听器
    watch(
      () => props.content,
      (newContent) => {
        if (props.isStreaming) {
          // 流式模式 - 重新开始渲染
          if (renderTimer.value) {
            clearTimeout(renderTimer.value)
          }
          startStreaming()
        } else {
          // 历史模式 - 直接渲染
          renderHistorical()
        }
      },
      { immediate: true }
    )
    
    watch(
      () => props.isStreaming,
      (isStreaming) => {
        if (isStreaming) {
          startStreaming()
        } else {
          stopStreaming()
        }
      }
    )
    
    // 生命周期
    onMounted(() => {
      if (props.content) {
        if (props.isStreaming) {
          startStreaming()
        } else {
          renderHistorical()
        }
      }
    })
    
    onUnmounted(() => {
      if (renderTimer.value) {
        clearTimeout(renderTimer.value)
      }
    })
    
    // 暴露方法
    const forceComplete = () => {
      stopStreaming()
    }
    
    const restart = () => {
      if (props.isStreaming) {
        startStreaming()
      }
    }
    
    
    return {
      displayContent,
      isTyping,
      markdownClass,
      handleCopy,
      forceComplete,
      restart
    }
  }
}
</script>

<style lang="scss" scoped>
.markdown-renderer {
  width: 100%;
  position: relative;
  
  &.streaming {
    .markdown-content {
      // 流式渲染时的特殊样式
      min-height: 20px;
    }
  }
  
  &.historical {
    .markdown-content {
      // 历史内容的完整样式
      opacity: 1;
    }
  }
}

.markdown-content {
  width: 100%;
  
  // XMarkdown 组件基础样式
  :deep(.x-markdown) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.7;
    color: #2c3e50;
    
    // 标题间距调整
    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 8px 0;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    // 段落间距
    p {
      margin: 8px 0;
    }
    
    // 列表间距
    ul, ol {
      margin: 8px 0;
      padding-left: 20px;
    }
    
    li {
      margin: 2px 0;
    }
    
    // 引用样式优化
    blockquote {
      border-left: 4px solid #ddd;
      padding-left: 16px;
      margin: 16px 0;
      color: #666;
    }
    
    // 代码块样式优化
    pre {
      background: #f8f8f8;
      padding: 12px;
      border-radius: 6px;
      overflow-x: auto;
      margin: 16px 0;
    }
    
    // 表格样式优化
    table {
      width: 100%;
      margin: 16px 0;
      
      th, td {
        padding: 8px 12px;
      }
    }
    
    // 分隔线样式
    hr {
      margin: 24px 0;
    }
  }
}

// 流式渲染光标
.streaming-cursor {
  display: inline-block;
  margin-left: 2px;
  
  .cursor-blink {
    font-weight: 100;
    color: #3498db;
    animation: blink 1s infinite;
  }
  
  &.typing .cursor-blink {
    animation: typing-blink 0.5s infinite;
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes typing-blink {
  0%, 30% { opacity: 1; }
  31%, 100% { opacity: 0.3; }
}

// 渲染完成指示器
.render-complete {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 13px;
  color: #0369a1;
  
  .complete-icon {
    color: #059669;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .markdown-content :deep(.x-markdown) {
    h1 { font-size: 1.5em; }
    h2 { font-size: 1.3em; }
    h3 { font-size: 1.1em; }
    
    pre {
      padding: 12px;
      margin: 12px 0;
      
      code {
        font-size: 0.85em;
      }
    }
    
    table {
      font-size: 0.9em;
      
      th, td {
        padding: 6px 8px;
      }
    }
  }
}
</style>