<template>
  <el-dialog
    v-model="visible"
    :title="$t('settings.title')"
    width="600px"
    :close-on-click-modal="false"
    class="settings-dialog"
  >
    <div class="settings-container">
      <!-- Tab Navigation -->
      <el-tabs v-model="activeTab" class="settings-tabs" :tab-position="'top'">
        <el-tab-pane :label="$t('settings.general')" name="general">
          <div class="settings-content">
            <!-- Language Setting -->
            <div class="setting-item">
              <div class="setting-label">
                <span class="label-text">{{ $t('settings.language') }}</span>
                <span class="label-desc">{{ $t('settings.languageDesc') }}</span>
              </div>
              <div class="setting-control">
                <el-select v-model="settings.language" :placeholder="$t('settings.language')" @change="handleLanguageChange">
                  <el-option :label="$t('common.chinese')" value="zh"></el-option>
                  <el-option :label="$t('common.english')" value="en"></el-option>
                </el-select>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- Placeholder for future tabs -->
        <el-tab-pane :label="$t('settings.account')" name="account" disabled>
          <div class="settings-content">
            <div class="coming-soon">{{ $t('settings.comingSoon') }}</div>
          </div>
        </el-tab-pane>

        <el-tab-pane :label="$t('settings.advanced')" name="advanced" disabled>
          <div class="settings-content">
            <div class="coming-soon">{{ $t('settings.comingSoon') }}</div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import { ref, reactive, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { useLanguageStore } from '../stores/language'

export default {
  name: 'SystemSettings',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'settings-changed'],
  setup(props, { emit }) {
    const { t } = useI18n()
    const visible = ref(props.modelValue)
    const activeTab = ref('general')
    const languageStore = useLanguageStore()
    
    // Settings data - sync with stores
    const settings = reactive({
      language: computed({
        get: () => languageStore.currentLanguage,
        set: (value) => languageStore.setLanguage(value)
      })
    })
    
    // Watch for prop changes
    watch(() => props.modelValue, (newVal) => {
      visible.value = newVal
    })
    
    // Watch for dialog visibility changes
    watch(visible, (newVal) => {
      emit('update:modelValue', newVal)
    })
    
    const handleLanguageChange = (value) => {
      const langName = value === 'zh' ? t('common.chinese') : t('common.english')
      ElMessage.success(t('settings.languageSwitched', { lang: langName }))
      emit('settings-changed', { type: 'language', value, settings: { ...settings } })
    }
    
    return {
      visible,
      activeTab,
      settings,
      handleLanguageChange
    }
  }
}
</script>

<style scoped>
.settings-container {
  min-height: 400px;
}

.settings-tabs {
  --el-tabs-header-height: 50px;
  flex-direction: column !important;
}

/* 修复el-tabs header和content反转问题 */
.settings-tabs .el-tabs__content {
  order: 2;
}

.settings-tabs .el-tabs__header {
  order: 1;
}

.settings-content {
  padding: 20px 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 0;
  border-bottom: 1px solid #e8eaed;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  flex: 1;
  padding-right: 20px;
}

.label-text {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
}

.label-desc {
  display: block;
  font-size: 14px;
  color: #5f6368;
  line-height: 1.4;
}

.setting-control {
  width: 200px;
  flex-shrink: 0;
}

.coming-soon {
  text-align: center;
  color: #9aa0a6;
  font-size: 14px;
  padding: 60px 0;
}
</style>

<style>
.settings-dialog .el-dialog {
  background-color: #ffffff !important;
  color: #202124 !important;
  border: 1px solid #dadce0 !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

.settings-dialog .el-dialog__header {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e8eaed !important;
}

.settings-dialog .el-dialog__title {
  color: #202124 !important;
}

.settings-dialog .el-dialog__body {
  padding: 20px 24px;
}

.settings-dialog .el-tabs__item.is-disabled {
  color: #9aa0a6;
}

.settings-dialog .el-select {
  width: 100%;
}

.settings-dialog .el-tabs__header {
  background-color: #ffffff;
}

.settings-dialog .el-tabs__nav-wrap::after {
  background-color: #dadce0;
}

.settings-dialog .el-tabs__item {
  color: #5f6368;
}

.settings-dialog .el-tabs__item.is-active {
  color: #1a73e8;
}

.settings-dialog .el-select .el-input__wrapper {
  background-color: #ffffff;
  border-color: #dadce0;
}

.settings-dialog .el-select .el-input__inner {
  color: #202124;
}

.settings-dialog .el-input__wrapper:hover {
  border-color: #d2d3d4;
}

.settings-dialog .el-input__wrapper.is-focus {
  border-color: #1a73e8;
}

.settings-dialog .el-select-dropdown {
  background-color: #ffffff !important;
  border-color: #dadce0 !important;
}

.settings-dialog .el-select-dropdown__item {
  color: #202124 !important;
  background-color: transparent !important;
}

.settings-dialog .el-select-dropdown__item:hover {
  background-color: #e9ecef !important;
}

.settings-dialog .el-select-dropdown__item.is-selected {
  background-color: #e8f0fe !important;
  color: #1a73e8 !important;
}
</style>