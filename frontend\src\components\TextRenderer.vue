<template>
  <div class="text-renderer" :class="{ 'streaming': isStreaming }">
    <!-- 纯文本内容显示 -->
    <div 
      :class="textClass"
      class="text-content"
    >
      {{ displayContent }}
    </div>
    
    <!-- 流式渲染时的光标指示器 -->
    <span 
      v-if="isStreaming && showCursor" 
      class="streaming-cursor"
      :class="{ 'typing': isTyping }"
    >|</span>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'

export default {
  name: 'TextRenderer',
  props: {
    // 原始文本内容
    content: {
      type: String,
      default: ''
    },
    
    // 是否为流式渲染模式
    isStreaming: {
      type: Boolean,
      default: false
    },
    
    // 流式渲染速度控制
    streamingSpeed: {
      type: Number,
      default: 30 // 毫秒
    },
    
    // 是否显示光标
    showCursor: {
      type: Boolean,
      default: true
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },
    
    // 是否保留换行
    preserveWhitespace: {
      type: Boolean,
      default: true
    }
  },
  
  emits: [
    'render-complete',
    'streaming-progress'
  ],
  
  setup(props, { emit }) {
    const displayContent = ref('')
    const isTyping = ref(false)
    const renderTimer = ref(null)
    const currentIndex = ref(0)
    
    // 计算样式类
    const textClass = computed(() => {
      return [
        props.customClass,
        {
          'preserve-whitespace': props.preserveWhitespace,
          'streaming-mode': props.isStreaming,
          'static-mode': !props.isStreaming
        }
      ]
    })
    
    // 流式渲染逻辑
    const startStreaming = () => {
      if (!props.isStreaming || !props.content) return
      
      currentIndex.value = 0
      displayContent.value = ''
      isTyping.value = true
      
      const streamContent = () => {
        if (currentIndex.value < props.content.length) {
          // 逐字符添加内容
          const nextChar = props.content[currentIndex.value]
          displayContent.value += nextChar
          currentIndex.value++
          
          // 发射进度事件
          emit('streaming-progress', {
            progress: (currentIndex.value / props.content.length) * 100,
            currentChar: nextChar,
            displayedContent: displayContent.value
          })
          
          // 智能速度控制 - 遇到标点符号稍微停顿
          const delay = ['.', '!', '?', '\n', '，', '。', '！', '？'].includes(nextChar) 
            ? props.streamingSpeed * 3 
            : props.streamingSpeed
            
          renderTimer.value = setTimeout(streamContent, delay)
        } else {
          // 渲染完成
          isTyping.value = false
          emit('render-complete', {
            content: displayContent.value,
            totalChars: currentIndex.value
          })
        }
      }
      
      streamContent()
    }
    
    // 停止流式渲染
    const stopStreaming = () => {
      if (renderTimer.value) {
        clearTimeout(renderTimer.value)
        renderTimer.value = null
      }
      isTyping.value = false
      displayContent.value = props.content
    }
    
    // 处理静态内容（非流式）
    const renderStatic = () => {
      displayContent.value = props.content
      nextTick(() => {
        emit('render-complete', {
          content: displayContent.value,
          isStatic: true
        })
      })
    }
    
    // 监听器
    watch(
      () => props.content,
      (newContent) => {
        if (props.isStreaming) {
          if (renderTimer.value) {
            clearTimeout(renderTimer.value)
          }
          startStreaming()
        } else {
          renderStatic()
        }
      },
      { immediate: true }
    )
    
    watch(
      () => props.isStreaming,
      (isStreaming) => {
        if (isStreaming) {
          startStreaming()
        } else {
          stopStreaming()
        }
      }
    )
    
    // 生命周期
    onMounted(() => {
      if (props.content) {
        if (props.isStreaming) {
          startStreaming()
        } else {
          renderStatic()
        }
      }
    })
    
    onUnmounted(() => {
      if (renderTimer.value) {
        clearTimeout(renderTimer.value)
      }
    })
    
    // 暴露方法
    const forceComplete = () => {
      stopStreaming()
    }
    
    const restart = () => {
      if (props.isStreaming) {
        startStreaming()
      }
    }
    
    return {
      displayContent,
      isTyping,
      textClass,
      forceComplete,
      restart
    }
  }
}
</script>

<style lang="scss" scoped>
.text-renderer {
  width: 100%;
  position: relative;
  
  &.streaming {
    .text-content {
      min-height: 20px;
    }
  }
}

.text-content {
  width: 100%;
  line-height: 1.7;
  color: #2c3e50;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  &.preserve-whitespace {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
  
  &.streaming-mode {
    // 流式渲染时的样式
  }
  
  &.static-mode {
    // 静态显示时的样式
  }
}

// 流式渲染光标
.streaming-cursor {
  display: inline-block;
  margin-left: 2px;
  font-weight: 100;
  color: #3498db;
  animation: blink 1s infinite;
  
  &.typing {
    animation: typing-blink 0.5s infinite;
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes typing-blink {
  0%, 30% { opacity: 1; }
  31%, 100% { opacity: 0.3; }
}

// 响应式适配
@media (max-width: 768px) {
  .text-content {
    font-size: 13px;
    line-height: 1.6;
  }
}
</style>