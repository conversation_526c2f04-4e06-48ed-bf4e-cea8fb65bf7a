<template>
  <div class="thinking-process" :class="{ 'collapsed': !showThinking }">
    <div class="thinking-header" @click="toggleThinking">
      <el-icon class="thinking-icon"><Operation /></el-icon>
      <span>思考过程</span>
      <span v-if="!showThinking && thinking" class="thinking-preview">
        {{ getThinkingPreview(thinking) }}
      </span>
      <el-button 
        link 
        size="small" 
        class="toggle-thinking"
      >
        <el-icon>
          <component :is="showThinking ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
      </el-button>
    </div>
    
    <el-collapse-transition>
      <div v-show="showThinking" class="thinking-content">
        <!-- 思考内容直接使用简单的HTML渲染 -->
        <div v-html="renderMarkdown(thinking || '正在思考中...')"></div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script>
import { Operation, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

export default {
  name: 'ThinkingProcess',
  components: {
    Operation,
    ArrowUp,
    ArrowDown
  },
  props: {
    // 思考内容
    thinking: {
      type: String,
      default: ''
    },
    
    // 是否展开思考过程
    showThinking: {
      type: Boolean,
      default: false
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  
  emits: [
    'update:showThinking'
  ],
  
  setup(props, { emit }) {
    
    // 切换展开/折叠状态
    const toggleThinking = () => {
      emit('update:showThinking', !props.showThinking)
    }
    
    // 获取思考过程的预览文本
    const getThinkingPreview = (thinking) => {
      if (!thinking) return ''
      // 移除markdown标记并截取前50个字符
      const plainText = thinking.replace(/[#*`\-\n]/g, ' ').replace(/\s+/g, ' ').trim()
      return plainText.length > 50 ? plainText.substring(0, 50) + '...' : plainText
    }
    
    // 简单的markdown渲染
    const renderMarkdown = (content) => {
      if (!content) return '';
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
    };
    
    return {
      toggleThinking,
      getThinkingPreview,
      renderMarkdown
    }
  }
}
</script>

<style lang="scss" scoped>
.thinking-process {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid #e1e8ff;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(98, 106, 239, 0.08);
  transition: all 0.3s ease;
  width: 100%;
  min-height: 48px; /* 确保有最小高度避免布局跳跃 */
  
  &:hover {
    box-shadow: 0 4px 16px rgba(98, 106, 239, 0.12);
  }
  
  &.collapsed {
    background: linear-gradient(135deg, #f0f4ff 0%, #e6f2ff 100%);
    border: 1px solid #d1e7ff;
    
    .thinking-header {
      background: linear-gradient(135deg, #f7faff 0%, #eef6ff 100%);
      color: #626aef;
      border-radius: 12px;
      margin: 0;
      
      &:hover {
        background: linear-gradient(135deg, #eff5ff 0%, #e6f2ff 100%);
      }
    }
    
    .thinking-icon {
      color: #626aef;
    }
    
    .thinking-preview {
      color: #8b92d4;
      opacity: 0.7;
    }
    
    .toggle-thinking {
      color: #626aef !important;
      
      &:hover {
        background: rgba(98, 106, 239, 0.1) !important;
      }
    }
  }
}

.thinking-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #626aef 0%, #7c83f0 100%);
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  user-select: none;
  
  &:hover {
    background: linear-gradient(135deg, #5a62e7 0%, #7479e8 100%);
  }
}

.thinking-icon {
  color: white;
  margin-right: 8px;
  font-size: 16px;
  opacity: 0.9;
  transition: color 0.3s ease;
}

.thinking-preview {
  flex: 1;
  margin: 0 12px;
  color: #8b92d4;
  font-size: 13px;
  font-weight: 400;
  font-style: italic;
  opacity: 0.8;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.toggle-thinking {
  margin-left: auto;
  padding: 4px !important;
  color: white !important;
  border-radius: 50%;
  transition: all 0.3s ease;
  min-width: 24px;
  height: 24px;
  flex-shrink: 0;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1);
  }
}

.thinking-content {
  padding: 16px 20px;
  font-size: 14px;
  line-height: 1.6;
  background: linear-gradient(135deg, #fafbff 0%, #f7f9ff 100%);
  border-top: 1px solid rgba(98, 106, 239, 0.1);
}

// 思考内容样式
.thinking-content {
  color: #6b7280;
  
  code {
    background: rgba(98, 106, 239, 0.1);
    color: #626aef;
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
  }
  
  strong {
    color: #4a5568;
    font-weight: 600;
  }
  
  em {
    color: #4a5568;
    font-style: italic;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .thinking-process {
    margin-bottom: 16px;
  }
  
  .thinking-header {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .thinking-content {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .thinking-preview {
    font-size: 12px;
    margin: 0 8px;
  }
}
</style>