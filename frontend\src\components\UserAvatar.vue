<template>
  <div>
    <el-popover
      :placement="collapsed ? 'right' : 'top'"
      :width="200"
      trigger="hover"
      popper-class="user-menu-popover"
    >
      <template #reference>
        <div class="user-info" :class="{ 'user-info-collapsed': collapsed }" @click="handleAvatarClick">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon v-if="!userAvatar"><User /></el-icon>
          </el-avatar>
          <span v-if="!collapsed" class="username">{{ username }}</span>
        </div>
      </template>
      
      <div class="user-menu">
        <!-- User display section -->
        <div class="user-display">
          <el-avatar :size="40" :src="userAvatar">
            <el-icon v-if="!userAvatar"><User /></el-icon>
          </el-avatar>
          <div class="user-name">{{ username }}</div>
        </div>
        
        <el-divider style="margin: 12px 0;" />
        
        <!-- Menu options -->
        <div class="menu-options">
          <div class="menu-item" @click="handleSystemSettings">
            <el-icon><Setting /></el-icon>
            <span>{{ $t('user.systemSettings') }}</span>
          </div>
          
          <div class="menu-item" @click="handleContactUs">
            <el-icon><Message /></el-icon>
            <span>{{ $t('user.contactUs') }}</span>
          </div>
          
          <div class="menu-item logout" @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
            <span>{{ $t('user.logout') }}</span>
          </div>
        </div>
      </div>
    </el-popover>

    <!-- System Settings Dialog -->
    <SystemSettings 
      v-model="showSettings" 
      @settings-changed="handleSettingsChanged"
    />
  </div>
</template>

<script>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserInfoStore } from '@/stores/userInfo'
import { useTokenStore } from '@/stores/token'
import { ElMessageBox, ElMessage } from 'element-plus'
import { User, Setting, Message, SwitchButton } from '@element-plus/icons-vue'
import SystemSettings from './SystemSettings.vue'

export default {
  name: 'UserAvatar',
  props: {
    collapsed: {
      type: Boolean,
      default: false
    }
  },
  components: {
    User,
    Setting,
    Message,
    SwitchButton,
    SystemSettings
  },
  setup(props) {
    const { t } = useI18n()
    const router = useRouter()
    const userStore = useUserInfoStore()
    const tokenStore = useTokenStore()
    
    const username = computed(() => userStore.username || t('user.user'))
    const userAvatar = computed(() => userStore.avatar || '')
    const showSettings = ref(false)
    
    const handleAvatarClick = () => {
      // Avatar click logic if needed
    }
    
    const handleSystemSettings = () => {
      showSettings.value = true
    }
    
    const handleSettingsChanged = (newSettings) => {
      // Settings changes are handled by stores
      console.log('Settings changed:', newSettings)
    }
    
    const handleContactUs = () => {
      ElMessage.info(t('settings.comingSoon'))
    }
    
    const handleLogout = async () => {
      try {
        await ElMessageBox.confirm(
          t('user.confirmLogout'),
          t('common.warning'),
          {
            confirmButtonText: t('common.confirm'),
            cancelButtonText: t('common.cancel'),
            type: 'warning',
          }
        )
        
        // Clear user info and token
        userStore.removeInfo()
        tokenStore.removeToken()
        
        // Navigate to login page
        router.push('/login')
        
        ElMessage.success(t('user.logoutSuccess'))
      } catch {
        // User cancelled
      }
    }
    
    return {
      username,
      userAvatar,
      showSettings,
      handleAvatarClick,
      handleSystemSettings,
      handleSettingsChanged,
      handleContactUs,
      handleLogout
    }
  }
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-info-collapsed {
  justify-content: center;
  padding: 8px 4px;
}

.username {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: white;
}

.user-menu {
  padding: 8px 0;
}

.user-display {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #202124;
}

.menu-options {
  padding: 0 8px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #5f6368;
}

.menu-item:hover {
  background-color: #e9ecef;
  color: #202124;
}

.menu-item.logout {
  color: #f56c6c;
}

.menu-item.logout:hover {
  background-color: #fef0f0;
  color: #f56c6c;
}

.menu-item .el-icon {
  font-size: 16px;
}
</style>

<style>
.user-menu-popover {
  padding: 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  background-color: #ffffff !important;
  border-color: #dadce0 !important;
}

.user-menu-popover .el-divider--horizontal {
  border-color: #e8eaed !important;
}

.user-menu-popover .el-popper__arrow::before {
  background-color: #ffffff !important;
  border-color: #dadce0 !important;
}

.user-menu-popover .el-popper__arrow::after {
  background-color: #ffffff !important;
}
</style>