<template>
  <div class="welcome-panel">
    <!-- 助手介绍区域 -->
    <div class="welcome-header">
      <div class="assistant-info">
        <div class="assistant-avatar">
          <span class="avatar-emoji">{{ assistantInfo.avatar || '🤖' }}</span>
        </div>
        <div class="assistant-details">
          <h2 class="welcome-title">{{ welcomeTitle }}</h2>
          <p class="assistant-description">{{ assistantInfo.description || '我是您的智能助手，可以帮助您完成各种任务。' }}</p>
        </div>
      </div>
    </div>

    <!-- 功能卡片区域 -->
    <div class="feature-cards">
      <div class="card-section">
        <h3 class="section-title">
          <el-icon class="section-icon"><Star /></el-icon>
          {{ leftSection.title }}
        </h3>
        <p class="section-subtitle">{{ leftSection.subtitle }}</p>
        <div class="card-list">
          <div 
            v-for="(card, index) in leftSection.cards"
            :key="index"
            class="feature-card"
            @click="handleCardClick(card)"
          >
            <el-icon class="card-icon">
              <component :is="card.icon" />
            </el-icon>
            <span class="card-text">{{ card.text }}</span>
          </div>
        </div>
      </div>

      <div class="card-section">
        <h3 class="section-title">
          <el-icon class="section-icon"><Tools /></el-icon>
          {{ rightSection.title }}
        </h3>
        <p class="section-subtitle">{{ rightSection.subtitle }}</p>
        <div class="card-list">
          <div 
            v-for="(card, index) in rightSection.cards"
            :key="index"
            class="feature-card"
            @click="handleCardClick(card)"
          >
            <el-icon class="card-icon">
              <component :is="card.icon" />
            </el-icon>
            <span class="card-text">{{ card.text }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { Star, Tools, QuestionFilled, ChatDotRound, Setting, Document, DataAnalysis, MagicStick } from '@element-plus/icons-vue'
import { useAssistantStore } from '@/stores/assistant'

export default {
  name: 'WelcomePanel',
  components: {
    Star,
    Tools,
    QuestionFilled,
    ChatDotRound,
    Setting,
    Document,
    DataAnalysis,
    MagicStick
  },
  props: {
    // 自定义助手信息
    customAssistant: {
      type: Object,
      default: null
    }
  },
  emits: ['card-click'],
  setup(props, { emit }) {
    const assistantStore = useAssistantStore()
    
    // 助手信息
    const assistantInfo = computed(() => {
      return props.customAssistant || assistantStore.selectedAssistant || {
        label: 'AI 助手',
        avatar: '🤖',
        description: '我是您的智能助手，可以帮助您完成各种任务，包括内容创作、代码编写、数据分析等。请告诉我您需要什么帮助！'
      }
    })

    // 欢迎标题
    const welcomeTitle = computed(() => {
      return `欢迎使用 ${assistantInfo.value.label}`
    })

    // 左侧区域配置
    const leftSection = {
      title: 'AI 应用场景',
      subtitle: '探索强大的AI功能',
      cards: [
        {
          icon: 'QuestionFilled',
          text: '如何使用 AI 创建应用?',
          query: '如何使用 AI 创建应用?'
        },
        {
          icon: 'ChatDotRound', 
          text: 'AI 支持哪些大语言模型?',
          query: 'AI 支持哪些大语言模型?'
        },
        {
          icon: 'Setting',
          text: '如何自定义 AI 助手的角色?',
          query: '如何自定义 AI 助手的角色?'
        }
      ]
    }

    // 右侧区域配置
    const rightSection = {
      title: '开发指南',
      subtitle: '快速上手开发',
      cards: [
        {
          icon: 'Document',
          text: 'API 接口使用说明',
          query: 'API 接口使用说明'
        },
        {
          icon: 'MagicStick',
          text: '如何集成到现有应用',
          query: '如何集成到现有应用'
        },
        {
          icon: 'DataAnalysis',
          text: '数据集管理最佳实践',
          query: '数据集管理最佳实践'
        }
      ]
    }

    // 处理卡片点击
    const handleCardClick = (card) => {
      emit('card-click', {
        text: card.text,
        query: card.query
      })
    }

    return {
      assistantInfo,
      welcomeTitle,
      leftSection,
      rightSection,
      handleCardClick
    }
  }
}
</script>

<style lang="scss" scoped>
.welcome-panel {
  max-width: 900px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-header {
  margin-bottom: 40px;
  
  .assistant-info {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    
    .assistant-avatar {
      flex-shrink: 0;
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
      
      .avatar-emoji {
        font-size: 28px;
      }
    }
    
    .assistant-details {
      flex: 1;
      
      .welcome-title {
        font-size: 28px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 12px 0;
        line-height: 1.2;
      }
      
      .assistant-description {
        font-size: 16px;
        color: #64748b;
        line-height: 1.6;
        margin: 0;
        max-width: 600px;
      }
    }
  }
}

.feature-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

.card-section {
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 6px 0;
    
    .section-icon {
      color: #409eff;
      font-size: 20px;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: #64748b;
    margin: 0 0 20px 0;
    font-weight: 400;
  }
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 18px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  
  .card-icon {
    flex-shrink: 0;
    font-size: 16px;
    color: #409eff;
    transition: color 0.3s ease;
  }
  
  .card-text {
    font-size: 14px;
    font-weight: 500;
    color: #334155;
    line-height: 1.4;
    transition: color 0.3s ease;
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .welcome-panel {
    padding: 20px 16px;
  }
  
  .welcome-header {
    margin-bottom: 30px;
    
    .assistant-info {
      .assistant-avatar {
        width: 56px;
        height: 56px;
        border-radius: 14px;
        
        .avatar-emoji {
          font-size: 24px;
        }
      }
      
      .assistant-details {
        .welcome-title {
          font-size: 24px;
        }
        
        .assistant-description {
          font-size: 15px;
        }
      }
    }
  }
  
  .card-section {
    .section-title {
      font-size: 16px;
    }
  }
  
  .feature-card {
    padding: 14px 16px;
    
    .card-text {
      font-size: 13px;
    }
  }
}
</style>