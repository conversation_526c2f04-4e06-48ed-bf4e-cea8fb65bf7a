<template>
  <div class="common-layout">
    <el-container>
      <el-header>
        <div class="header-left">
          <img src="/logo.svg" alt="Logo" class="logo" />
          <span class="hospital-name">{{ config.hospital.name }}</span>
        </div>
        <div class="header-right">
          <UserAvatar />
        </div>
      </el-header>
      <el-container>
        <el-aside v-if="showSidebar" :width="isCollapsed ? '60px' : '280px'">
          <div class="sidebar-header" :class="{ 'collapsed': isCollapsed }">
            <div class="left-side-header" v-if="!isCollapsed">
              <!-- 助手选择器组件 -->
              <AssistantSelector @assistant-changed="handleAssistantChanged" />
            </div>
            <el-button @click="toggleSidebar" size="small" class="toggle-btn">
              <el-icon>
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path v-if="isCollapsed" d="M9 18l6-6-6-6" />
                  <path v-else d="M15 18l-6-6 6-6" />
                </svg>
              </el-icon>
            </el-button>
          </div>
          
          <div class="sidebar-content">
            <!-- 助手选择和新对话按钮 -->
            <div class="to-assiatant-chose">
              <template v-if="!isCollapsed">
                <el-button
                  class="assistant-select-btn"
                  type="default"
                  @click="handleGoToAssistantSelect"
                >
                  <el-icon><User /></el-icon>
                  {{ $t("nav.selectAssistant") }}
                </el-button>
                <el-button
                  class="new-chat-btn"
                  type="primary"
                  plain
                  @click="handleNewChat"
                >
                  <el-icon><Plus /></el-icon>
                  {{ $t("nav.newChat") }}
                </el-button>
              </template>
              <template v-else>
                <el-button 
                  class="assistant-select-btn-collapsed" 
                  type="default"
                  @click="handleGoToAssistantSelect"
                >
                  <el-icon><User /></el-icon>
                </el-button>
                <el-button 
                  class="new-chat-btn-collapsed" 
                  type="primary" 
                  plain
                  @click="handleNewChat"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </template>
            </div>
            
            <!-- 对话历史 -->
            <div v-if="!isCollapsed" class="chat-history">
              <Conversations
                ref="conversationsRef"
                :active="activeConversationId"
                :items="chatHistory"
                groupable
                :label-max-width="200"
                :show-tooltip="!isCollapsed"
                tooltip-placement="right"
                :tooltip-offset="35"
                row-key="conversationId"
                show-built-in-menu
                show-to-top-btn
                :ungrouped-title="$t('chat.ungrouped')"
                :loadMore="handleLoadMore"
                @change="handleConversationChange"
                @update:active="activeConversationId = $event"
                @menu-command="handleMenuCommand"
              />
              <div v-if="isLoading" class="loading-indicator">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
            </div>
          </div>
        </el-aside>
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>

  </div>
</template>

<script>
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserInfoStore } from '@/stores/userInfo'
import { Plus, User, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { Conversations } from 'vue-element-plus-x'
import config from '@/config'
import UserAvatar from '@/components/UserAvatar.vue'
import AssistantSelector from '@/components/AssistantSelector.vue'
import { useConversationStore } from '@/stores/conversation'
import { useAssistantStore } from '@/stores/assistant'
import { conversationAPI } from '@/api'

export default {
  name: 'MainLayout',
  components: {
    UserAvatar,
    Conversations,
    Plus,
    User,
    Loading,
    AssistantSelector
  },
  setup() {
    const { t } = useI18n()
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserInfoStore()
    const conversationStore = useConversationStore()
    const assistantStore = useAssistantStore()
    const isCollapsed = ref(false)
    const conversationsRef = ref(null)
    
    // 直接管理会话列表数据
    const chatHistory = ref([])
    const isLoading = ref(false)
    const currentPage = ref(1)
    const hasMore = ref(true)
    const pageSize = ref(20)
    
    // 处理助手改变事件
    const handleAssistantChanged = async (assistant) => {
      // 切换助手后重新加载对话列表
      await loadConversations(assistant.id, 1, false)
    }

    // 加载会话列表
    const loadConversations = async (assistantId, page = 1, append = false) => {
      if (!assistantId) {
        chatHistory.value = []
        return
      }
      
      if (isLoading.value) return
      
      isLoading.value = true
      
      try {
        const userId = userStore.userId
        const response = await conversationAPI.getConversations(assistantId, userId, page, pageSize.value)
        const { conversations, pagination } = response
        
        console.log('API Response:', { conversations, pagination })
        
        // 将后端返回的数据转换为Conversations组件需要的格式（统一使用驼峰命名）
        const formattedConversations = conversations.map(conv => ({
          conversationId: conv.conversation_id,
          userId: conv.user_id,
          assistantId: conv.assistant_id,
          name: conv.name,
          createdAt: conv.created_at,
          group: conv.group,
          // 组件需要的字段
          label: conv.name || conv.title
        }))
        
        // 设置选中状态

        if (append) {
          // 追加数据（滚动懒加载）
          chatHistory.value.push(...formattedConversations)
        } else {
          // 替换数据（初次加载）
          chatHistory.value = formattedConversations
        }
        
        console.log('Updated chatHistory:', chatHistory.value)
        
        // 更新分页信息
        currentPage.value = pagination.page
        hasMore.value = pagination.has_more
        
      } catch (error) {
        console.error('Failed to load conversations:', error)
        if (!append) {
          chatHistory.value = []
        }
      } finally {
        isLoading.value = false
      }
    }
    
    // 懒加载回调函数
    const handleLoadMore = async () => {
      console.log('Load more triggered', { hasMore: hasMore.value, isLoading: isLoading.value })
      
      if (!hasMore.value || isLoading.value) return
      
      const assistantId = assistantStore.selectedAssistant?.id
      if (!assistantId) return
      
      console.log('Loading more conversations for assistant:', assistantId, 'page:', currentPage.value + 1)
      
      await loadConversations(assistantId, currentPage.value + 1, true)
    }
    
    // 刷新当前助手的会话列表
    const refreshConversations = async (smooth = false) => {
      const assistantId = assistantStore.selectedAssistant?.id
      if (assistantId) {
        // 重置分页状态
        currentPage.value = 1
        hasMore.value = true
        
        if (smooth) {
          // 平滑刷新：先加载新数据，再替换，避免闪烁
          try {
            const response = await conversationAPI.getConversations(assistantId, 1, pageSize.value)
            const { conversations } = response
            
            // 转换格式
            const formattedConversations = conversations.map(conv => ({
              ...conv,
              label: conv.title,
              assistantId: conv.assistant_id
            }))
            
            // 一次性替换数据，避免清空阶段的闪烁
            chatHistory.value = formattedConversations
            
          } catch (error) {
            console.error('Failed to refresh conversations:', error)
          }
        } else {
          // 常规刷新：清空现有数据
          chatHistory.value = []
          
          // 滚动到顶部
          await nextTick()
          if (conversationsRef.value && conversationsRef.value.scrollToTop) {
            conversationsRef.value.scrollToTop()
          }
          
          // 加载新数据
          await loadConversations(assistantId)
        }
      }
    }

    // 当选择的助手改变时，加载对应的会话列表
    watch(() => assistantStore.selectedAssistant, async (newAssistant) => {
      await refreshConversations()
    }, { immediate: true })

    // 监听新对话创建事件
    const handleConversationCreated = (event) => {
      const { assistantId } = event.detail
      // 如果新对话属于当前助手，刷新会话列表
      if (assistantId === assistantStore.selectedAssistant?.id) {
        refreshConversations()
      }
    }

    onMounted(() => {
      window.addEventListener('conversationCreated', handleConversationCreated)
    })

    onUnmounted(() => {
      window.removeEventListener('conversationCreated', handleConversationCreated)
    })
    
    // Active conversation - 从路由参数获取，如果没有则默认为null
    const activeConversationId = ref(route.params.conversationId || null)
    
    // 根据路由决定是否显示侧边栏
    const showSidebar = computed(() => {
      // 只有在聊天页面才显示侧边栏
      return route.name === 'Chat'
    })
    
    // 监听路由参数变化，更新激活的会话
    watch(() => route.params.conversationId, (newConversationId) => {
      activeConversationId.value = newConversationId || null
    }, { immediate: true })
    
    const toggleSidebar = () => {
      isCollapsed.value = !isCollapsed.value
    }
    
    // Handle new chat button click
    const handleNewChat = async () => {
      // 检查是否已选择助手
      if (!assistantStore.hasSelectedAssistant) {
        ElMessage.warning('请先选择助手')
        return
      }
      
      try {
        // 调用API创建新对话
        const userId = userStore.userId
        const newConversation = await conversationAPI.createConversation(assistantStore.selectedAssistant.id, userId)
        
        // 更新会话状态
        conversationStore.setCurrentConversation(newConversation.conversationId)
        
        // 设置活跃会话
        activeConversationId.value = newConversation.conversationId
        
        // 路由跳转到新对话页面（带ID）
        router.push(`/chat/${newConversation.conversationId}`)
        
        // 优化：直接将新对话添加到列表顶部，避免完全刷新（统一使用驼峰命名）
        const formattedConversation = {
          conversationId: newConversation.conversationId,
          userId: newConversation.userId,
          assistantId: newConversation.assistantId,
          name: newConversation.name,
          createdAt: newConversation.createdAt,
          group: 'today', // 新对话归到今天分组
          // 组件需要的字段
          label: newConversation.name
        }
        chatHistory.value.unshift(formattedConversation)
        
        ElMessage.success('新对话已创建')
      } catch (error) {
        console.error('Failed to create new conversation:', error)
        ElMessage.error('创建对话失败，请重试')
      }
    }
    
    // Handle go to assistant select
    const handleGoToAssistantSelect = () => {
      router.push('/')
    }
    
    // Handle conversation selection
    const handleConversationChange = (item) => {
      console.log("会话切换：", item)
      activeConversationId.value = item.conversationId
      
      // 通过路由跳转切换会话
      router.push(`/chat/${item.conversationId}`)
    }
    
    // Handle menu commands
    const handleMenuCommand = (command, item) => {
      console.log("菜单命令：", command, item)

      switch (command) {
        case "rename":
          handleRename(item)
          break
        case "delete":
          handleDelete(item)
          break
        case "pin":
          handlePin(item)
          break
        case "archive":
          handleArchive(item)
          break
        default:
          console.log("未知命令：", command)
      }
    }
    
    // Rename conversation
    const handleRename = async (item) => {
      const newName = prompt("请输入新的会话名称：", item.label)
      if (newName && newName.trim()) {
        try {
          // 调用API更新标题
          await conversationAPI.updateConversationTitle(item.conversationId, newName.trim())
          
          // 更新本地数据
          const conversation = chatHistory.value.find(conv => conv.conversationId === item.conversationId)
          if (conversation) {
            conversation.label = newName.trim()
          }
          
          ElMessage.success("重命名成功")
        } catch (error) {
          console.error('Failed to rename conversation:', error)
          ElMessage.error("重命名失败，请重试")
        }
      }
    }
    
    // Delete conversation
    const handleDelete = async (item) => {
      try {
        // 调用API删除会话
        await conversationAPI.deleteConversation(item.conversationId)
        
        // 从本地列表中移除
        const index = chatHistory.value.findIndex(conv => conv.conversationId === item.conversationId)
        if (index !== -1) {
          chatHistory.value.splice(index, 1)
        }
        
        ElMessage.success("删除成功")

        // If deleted conversation was active, switch to first available or go to new chat
        if (activeConversationId.value === item.conversationId) {
          if (chatHistory.value.length > 0) {
            const firstConversation = chatHistory.value[0]
            activeConversationId.value = firstConversation.conversationId
            router.push(`/chat/${firstConversation.conversationId}`)
          } else {
            // 没有其他对话，回到新对话页面
            activeConversationId.value = null
            router.push('/chat')
          }
        }
      } catch (error) {
        console.error('Failed to delete conversation:', error)
        ElMessage.error("删除失败，请重试")
      }
    }
    
    // Pin conversation
    const handlePin = (item) => {
      ElMessage.info("置顶功能待实现")
    }
    
    // Archive conversation
    const handleArchive = (item) => {
      ElMessage.info("归档功能待实现")
    }
    
    return {
      isCollapsed,
      showSidebar,
      toggleSidebar,
      config,
      chatHistory,
      activeConversationId,
      isLoading,
      conversationsRef,
      assistantStore,
      handleNewChat,
      handleGoToAssistantSelect,
      handleConversationChange,
      handleMenuCommand,
      handleLoadMore,
      handleAssistantChanged
    }
  }
}
</script>

<style lang="scss" scoped>
.common-layout {
  height: 100vh;
  overflow: hidden; /* 防止整个页面滚动 */
  
  .el-container {
    height: 100%;
    
    .el-header {
      background-color: #409EFF;
      color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .logo {
          height: 40px;
          width: auto;
        }
        
        .hospital-name {
          font-size: 18px;
          font-weight: 500;
          white-space: nowrap;
        }
      }
      
      .header-right {
        display: flex;
        align-items: center;
      }
    }
    
    .el-aside {
      background-color: #f5f7fa;
      border-right: 1px solid #e4e7ed;
      transition: width 0.3s ease;
      display: flex;
      flex-direction: column;
      padding: 0;
      height: calc(100vh - 60px); /* 减去header高度 */
      overflow: hidden;
      
      .sidebar-header {
        padding: 5px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e4e7ed;
        
        &.collapsed {
          justify-content: center;
        }
        
        .left-side-header {
          flex: 1;
          display: flex;
          align-items: center;
          margin-right: 20px;
        }
        
        .toggle-btn {
          height: 32px;
          width: 32px;
          color: #5f6368;
          border: 1px solid #e8eaed;
          border-radius: 6px;
          background-color: #ffffff;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &:hover {
            background-color: #f8f9fa;
            border-color: #d2d3d4;
            color: #202124;
          }
          
          &:active {
            transform: scale(0.95);
          }
          
          .el-icon {
            font-size: 16px;
            
            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }
      
      .sidebar-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        min-height: 0; /* 允许flex子元素收缩 */
      }

      .to-assiatant-chose {
        padding: 10px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        flex-shrink: 0; /* 防止按钮区域被压缩 */
        
        .assistant-select-btn {
          padding: 20px 10px;
          border-radius: 10px;
          width: 100%;
          border: 1px solid #e4e7ed;
          color: #606266;
          background-color: #ffffff;
          transition: all 0.3s ease;
          margin-left: 0 !important; /* 覆盖 Element Plus 默认的按钮间距 */
          
          &:hover {
            background-color: #f5f7fa;
            border-color: #c0c4cc;
          }

          .el-icon {
            font-size: 16px;
            margin-right: 8px;
            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
        
        .new-chat-btn {
          padding: 20px 10px;
          border-radius: 10px;
          width: 100%;
          border: 1px solid #e4e7ed;
          color: #606266;
          background-color: #ffffff;
          transition: all 0.3s ease;
          margin-left: 0 !important; /* 覆盖 Element Plus 默认的按钮间距 */
          
          &:hover {
            background-color: #f5f7fa;
            border-color: #c0c4cc;
          }

          .el-icon {
            font-size: 16px;
            margin-right: 8px;
            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
        
        .assistant-select-btn-collapsed,
        .new-chat-btn-collapsed {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #606266;
          background-color: #ffffff;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;
          margin-left: 0 !important; /* 覆盖 Element Plus 默认的按钮间距 */
          
          &:hover {
            background-color: #f5f7fa;
            border-color: #c0c4cc;
          }
        }
      }
      
      
      .chat-history {
        flex: 1;
        min-height: 0;
        overflow-y: auto; /* 启用垂直滚动 */
        overflow-x: hidden; /* 隐藏水平滚动 */
        
        
        /* 强制 Conversations 组件适应容器 */
        :deep(.conversations-container) {
          height: 100%;
          overflow: hidden;
        }
        
        :deep(.conversations-list) {
          height: 100%;
          overflow-y: auto;
          
          /* 确保列表容器的稳定高度 */
          min-height: 100px;
        }
        
        .loading-indicator {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 16px;
          color: #909399;
          font-size: 14px;
          
          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
    
    .el-main {
      background-color: #ffffff;
      padding: 0;
      margin: 0;
      overflow-y: auto; /* 主内容区域可滚动 */
      height: calc(100vh - 60px); /* 减去header高度 */
    }
  }
}

</style>