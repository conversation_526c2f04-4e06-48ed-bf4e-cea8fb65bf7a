{"app": {"title": "Research Assistant", "loading": "Loading..."}, "nav": {"home": "Home", "newChat": "New chat", "selectAssistant": "Select Assistant"}, "user": {"user": "User", "systemSettings": "System Settings", "contactUs": "Contact Us", "logout": "Logout", "confirmLogout": "Are you sure you want to logout?", "logoutSuccess": "Logged out successfully"}, "settings": {"title": "System Settings", "general": "General Settings", "account": "Account <PERSON><PERSON>", "advanced": "Advanced Settings", "language": "Language", "languageDesc": "Select interface display language", "theme": "Theme", "themeDesc": "Select interface theme style", "light": "Light", "dark": "Dark", "comingSoon": "Feature coming soon...", "languageSwitched": "Language switched to: {lang}", "themeSwitched": "Theme switched to: {theme}"}, "login": {"title": "Research Assistant", "subtitle": "Log in to continue", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "usernameRequired": "Please enter username", "passwordRequired": "Please enter password", "loginSuccess": "Login successful"}, "chat": {"placeholder": "Message Research Assistant...", "you": "You", "assistant": "Research Assistant", "greeting": "Hello! I'm your research assistant. How can I help you today?", "simulatedResponse": "I received your message: \"{message}\". This is a simulated response. In a real application, this would be handled by an actual API call to get a proper response.", "ungrouped": "Ungrouped", "startTip": "You can start asking me questions, and I'll provide assistance based on my specialized expertise.", "welcomeTitle": "I'm {assistant<PERSON><PERSON>} assistant, nice to meet you!"}, "assistants": {"selectTitle": "<PERSON><PERSON> Your Research Assistant", "currentAssistant": "Current Assistant", "switchAssistant": "Switch Assistant", "pleaseSelectFirst": "Please select an assistant type first", "literature": {"title": "📚 Literature Search & Analysis", "description": "Help you search, filter and interpret academic literature, provide literature reviews and research trend analysis"}, "query": {"title": "🔍 Query Assistant", "description": "Quickly answer various academic questions, provide accurate knowledge queries and solutions"}, "dataProcessing": {"title": "⚙️ Data Processing Assistant", "description": "Assist you with data cleaning, transformation and preprocessing, support multiple data formats"}, "dataAnalysis": {"title": "📊 Data Analysis Assistant", "description": "Provide statistical analysis, visualization and machine learning data analysis services"}, "report": {"title": "📝 Report Assistant", "description": "Help you write research reports, papers and various academic documents, provide writing guidance"}}, "common": {"confirm": "Confirm", "cancel": "Cancel", "save": "Save", "close": "Close", "warning": "Warning", "english": "English", "chinese": "Chinese"}}