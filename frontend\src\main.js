import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlusX from 'vue-element-plus-x'
import App from './App.vue'
import router from './router'
import '@/styles/reset.scss'
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import i18n from '@/locales'

// Pinia setup
const pinia = createPinia()
pinia.use(createPersistedState())

const app = createApp(App)

app.use(ElementPlus)
app.use(ElementPlusX)
app.use(router)
app.use(pinia)
app.use(i18n)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')