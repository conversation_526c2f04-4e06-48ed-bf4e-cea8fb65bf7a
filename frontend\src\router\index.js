import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/layout/MainLayout.vue'
import LoginView from '@/views/login/LoginView.vue'
import AssistantSelect from '@/views/AssistantSelect.vue'
import Chat from '@/views/Chat.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: LoginView
  },
  {
    path: '/tools-build',
    name: 'ToolsBuild',
    component: () => import('@/views/generator/tool-build/index.vue')
  },
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'AssistantSelect',
        component: AssistantSelect
      },
      {
        path: 'chat/:conversationId?',
        name: 'Chat',
        component: Chat
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router