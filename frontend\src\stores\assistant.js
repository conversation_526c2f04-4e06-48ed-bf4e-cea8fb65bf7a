import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useAssistantStore = defineStore('assistant', () => {
    const selectedAssistant = ref(null);
    const showAssistantSelector = ref(true);
    const assistantsList = ref([]); // 缓存助手列表
    
    const setAssistant = (assistant) => {
        selectedAssistant.value = assistant;
        showAssistantSelector.value = false;
    };
    
    const clearAssistant = () => {
        selectedAssistant.value = null;
        showAssistantSelector.value = true;
    };
    
    const switchAssistant = () => {
        showAssistantSelector.value = true;
        selectedAssistant.value = null;
    };
    
    // 设置助手列表（从API获取后缓存）
    const setAssistantsList = (assistants) => {
        assistantsList.value = assistants;
    };
    
    // 根据ID切换助手
    const switchToAssistant = (assistantId) => {
        const assistant = assistantsList.value.find(a => a.id === assistantId);
        if (assistant) {
            setAssistant(assistant);
        }
    };
    
    // 计算属性：是否已选择助手
    const hasSelectedAssistant = computed(() => !!selectedAssistant.value);
    
    // 计算属性：当前助手名称
    const currentAssistantName = computed(() => selectedAssistant.value?.label || '');
    
    // 计算属性：是否有缓存的助手列表
    const hasAssistantsList = computed(() => assistantsList.value.length > 0);
    
    return {
        selectedAssistant,
        showAssistantSelector,
        assistantsList,
        hasSelectedAssistant,
        currentAssistantName,
        hasAssistantsList,
        setAssistant,
        clearAssistant,
        switchAssistant,
        setAssistantsList,
        switchToAssistant
    };
}, { persist: true });

export default useAssistantStore;