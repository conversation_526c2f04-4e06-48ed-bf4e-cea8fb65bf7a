import { defineStore } from "pinia";
import { ref } from "vue";

export const useConversationStore = defineStore('conversation', () => {
    // 只保留当前会话ID状态
    const currentConversationId = ref(null);
    
    // 设置当前会话ID
    const setCurrentConversation = (id) => {
        currentConversationId.value = id;
    };
    
    // 重置当前会话（用于新对话）
    const resetCurrentConversation = () => {
        currentConversationId.value = null;
    };
    
    return {
        // 状态
        currentConversationId,
        
        // 方法
        setCurrentConversation,
        resetCurrentConversation
    };
});

export default useConversationStore;