import { defineStore } from "pinia";
import { ref } from "vue";
import i18n from '../locales'

export const useLanguageStore = defineStore('language', () => {
    const currentLanguage = ref('zh'); // 默认中文
    
    const setLanguage = (lang) => {
        currentLanguage.value = lang;
        // 切换 i18n 的语言
        i18n.global.locale.value = lang;
        // 更新 HTML lang 属性
        document.documentElement.lang = lang;
    };
    
    const toggleLanguage = () => {
        const newLang = currentLanguage.value === 'zh' ? 'en' : 'zh';
        setLanguage(newLang);
    };
    
    // 初始化语言设置
    const initLanguage = () => {
        setLanguage(currentLanguage.value);
    };
    
    // 获取当前语言的显示名称
    const getCurrentLanguageName = () => {
        return currentLanguage.value === 'zh' ? '中文' : 'English';
    };
    
    return {
        currentLanguage,
        setLanguage,
        toggleLanguage,
        initLanguage,
        getCurrentLanguageName
    };
}, { persist: true });

export default useLanguageStore;