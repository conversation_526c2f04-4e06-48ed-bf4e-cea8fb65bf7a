import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useUserInfoStore = defineStore('userInfo', () => {
    // const info = ref({});

    // TODO 用户先写死
    const info = ref({
        id: "111111",
        username: 'admin',
        avatar: 'https://avatars.githubusercontent.com/u/10656201?s=200&v=4',
        name: '管理员'
    });
    const setInfo = (newInfo) => {
        info.value = newInfo;
    };

    const removeInfo = () => {
        info.value = {};
    };

    // Computed properties for easy access to common user info
    const username = computed(() => {
        return info.value.username || info.value.name || '';
    });
    const avatar = computed(() => info.value.avatar || '');

    const userId = computed(() => {
        if (info.value.id) {
            return info.value.id;
        }

        return info.value.userId || ''
    });

    return {
        info,
        setInfo,
        removeInfo,
        username,
        avatar,
        userId
    };
}, { persist: true });

export default useUserInfoStore;