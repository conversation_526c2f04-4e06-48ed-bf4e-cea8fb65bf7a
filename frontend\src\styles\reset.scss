/* CSS Reset - 去除HTML标签默认样式 */

/* 1. 盒模型重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 2. 移除默认边距和内边距 */
* {
  margin: 0;
  padding: 0;
}

/* 3. HTML和body基础设置 */
html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* 15. 移除WebKit浏览器的默认样式 */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 16. Firefox输入框重置 */
input[type="number"] {
  -moz-appearance: textfield;
}

/* 17. 移除IE10+的x按钮 */
input::-ms-clear {
  display: none;
}

/* 18. 移除IE的密码显示按钮 */
input::-ms-reveal {
  display: none;
}

/* 19. 统一的滚动条样式（全局默认） */
* {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* 20. 文本选择样式重置 */
::selection {
  background: #b3d4fc;
  text-shadow: none;
}