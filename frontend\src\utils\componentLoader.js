
// ISCII对照表：  https://tool.oschina.net/commons?type=4
// 模拟LLM调用


const shen_gong_neng = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>肾功能单位换算工具</title>
    <style>
        :root {
            --primary-color: #4a6bff;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --light-text: #6c757d;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --accent-color: #ff7e5f;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--secondary-color);
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .description {
            text-align: center;
            color: var(--light-text);
            margin-bottom: 30px;
            font-size: 16px;
        }

        .converter-section {
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            background-color: white;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--accent-color);
        }

        .input-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .input-field {
            flex: 1;
            min-width: 200px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-color);
        }

        input, select {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 107, 255, 0.2);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: var(--light-color);
            border-radius: 5px;
            font-size: 16px;
            display: none;
        }

        .result.active {
            display: block;
        }

        .result-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background-color 0.3s;
            display: block;
            width: 100%;
            margin-top: 20px;
        }

        button:hover {
            background-color: #3a5bef;
        }

        .info-box {
            background-color: var(--light-color);
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin-top: 30px;
            border-radius: 0 5px 5px 0;
        }

        .info-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .info-text {
            color: var(--light-text);
            font-size: 14px;
        }

        @media (max-width: 600px) {
            .container {
                padding: 15px;
            }
            
            .input-field {
                min-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>肾功能单位换算工具</h1>
        <p class="description">在尿酸、肌酐和尿素氮的不同单位之间进行换算</p>
        
        <div class="converter-section">
            <div class="section-title">
                <span>尿酸单位换算</span>
            </div>
            <div class="input-group">
                <div class="input-field">
                    <label for="ua-value">尿酸值</label>
                    <input type="number" id="ua-value" placeholder="输入数值">
                </div>
                <div class="input-field">
                    <label for="ua-from-unit">从单位</label>
                    <select id="ua-from-unit">
                        <option value="mg/dL">mg/dL</option>
                        <option value="μmol/L">μmol/L</option>
                    </select>
                </div>
                <div class="input-field">
                    <label for="ua-to-unit">转换为</label>
                    <select id="ua-to-unit">
                        <option value="μmol/L">μmol/L</option>
                        <option value="mg/dL">mg/dL</option>
                    </select>
                </div>
            </div>
            <div class="result" id="ua-result">
                换算结果: <span class="result-value" id="ua-result-value"></span>
            </div>
        </div>
        
        <div class="converter-section">
            <div class="section-title">
                <span>肌酐单位换算</span>
            </div>
            <div class="input-group">
                <div class="input-field">
                    <label for="cr-value">肌酐值</label>
                    <input type="number" id="cr-value" placeholder="输入数值">
                </div>
                <div class="input-field">
                    <label for="cr-from-unit">从单位</label>
                    <select id="cr-from-unit">
                        <option value="mg/dL">mg/dL</option>
                        <option value="μmol/L">μmol/L</option>
                    </select>
                </div>
                <div class="input-field">
                    <label for="cr-to-unit">转换为</label>
                    <select id="cr-to-unit">
                        <option value="μmol/L">μmol/L</option>
                        <option value="mg/dL">mg/dL</option>
                    </select>
                </div>
            </div>
            <div class="result" id="cr-result">
                换算结果: <span class="result-value" id="cr-result-value"></span>
            </div>
        </div>
        
        <div class="converter-section">
            <div class="section-title">
                <span>尿素氮单位换算</span>
            </div>
            <div class="input-group">
                <div class="input-field">
                    <label for="bun-value">尿素氮值</label>
                    <input type="number" id="bun-value" placeholder="输入数值">
                </div>
                <div class="input-field">
                    <label for="bun-from-unit">从单位</label>
                    <select id="bun-from-unit">
                        <option value="mg/dL">mg/dL</option>
                        <option value="mmol/L">mmol/L</option>
                    </select>
                </div>
                <div class="input-field">
                    <label for="bun-to-unit">转换为</label>
                    <select id="bun-to-unit">
                        <option value="mmol/L">mmol/L</option>
                        <option value="mg/dL">mg/dL</option>
                    </select>
                </div>
            </div>
            <div class="result" id="bun-result">
                换算结果: <span class="result-value" id="bun-result-value"></span>
            </div>
        </div>
        
        <button id="calculate">计算换算</button>
        
        <div class="info-box">
            <div class="info-title">换算公式说明</div>
            <div class="info-text">
                <p><strong>尿酸:</strong> 1 mg/dL = 59.48 μmol/L</p>
                <p><strong>肌酐:</strong> 1 mg/dL = 88.4 μmol/L</p>
                <p><strong>尿素氮:</strong> 1 mg/dL = 0.357 mmol/L</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取计算按钮
            const calculateBtn = document.getElementById('calculate');
            
            // 添加点击事件
            calculateBtn.addEventListener('click', function() {
                // 尿酸换算
                const uaValue = parseFloat(document.getElementById('ua-value').value);
                const uaFromUnit = document.getElementById('ua-from-unit').value;
                const uaToUnit = document.getElementById('ua-to-unit').value;
                const uaResult = convertUricAcid(uaValue, uaFromUnit, uaToUnit);
                
                if (!isNaN(uaResult)) {
                    document.getElementById('ua-result-value').textContent = uaResult.toFixed(2) + ' ' + uaToUnit;
                    document.getElementById('ua-result').classList.add('active');
                }
                
                // 肌酐换算
                const crValue = parseFloat(document.getElementById('cr-value').value);
                const crFromUnit = document.getElementById('cr-from-unit').value;
                const crToUnit = document.getElementById('cr-to-unit').value;
                const crResult = convertCreatinine(crValue, crFromUnit, crToUnit);
                
                if (!isNaN(crResult)) {
                    document.getElementById('cr-result-value').textContent = crResult.toFixed(2) + ' ' + crToUnit;
                    document.getElementById('cr-result').classList.add('active');
                }
                
                // 尿素氮换算
                const bunValue = parseFloat(document.getElementById('bun-value').value);
                const bunFromUnit = document.getElementById('bun-from-unit').value;
                const bunToUnit = document.getElementById('bun-to-unit').value;
                const bunResult = convertBUN(bunValue, bunFromUnit, bunToUnit);
                
                if (!isNaN(bunResult)) {
                    document.getElementById('bun-result-value').textContent = bunResult.toFixed(2) + ' ' + bunToUnit;
                    document.getElementById('bun-result').classList.add('active');
                }
            });
            
            // 尿酸换算函数
            function convertUricAcid(value, fromUnit, toUnit) {
                if (isNaN(value)) return NaN;
                
                // 尿酸换算系数: 1 mg/dL = 59.48 μmol/L
                const conversionFactor = 59.48;
                
                if (fromUnit === 'mg/dL' && toUnit === 'μmol/L') {
                    return value * conversionFactor;
                } else if (fromUnit === 'μmol/L' && toUnit === 'mg/dL') {
                    return value / conversionFactor;
                }
                
                return value; // 如果单位相同
            }
            
            // 肌酐换算函数
            function convertCreatinine(value, fromUnit, toUnit) {
                if (isNaN(value)) return NaN;
                
                // 肌酐换算系数: 1 mg/dL = 88.4 μmol/L
                const conversionFactor = 88.4;
                
                if (fromUnit === 'mg/dL' && toUnit === 'μmol/L') {
                    return value * conversionFactor;
                } else if (fromUnit === 'μmol/L' && toUnit === 'mg/dL') {
                    return value / conversionFactor;
                }
                
                return value; // 如果单位相同
            }
            
            // 尿素氮换算函数
            function convertBUN(value, fromUnit, toUnit) {
                if (isNaN(value)) return NaN;
                
                // 尿素氮换算系数: 1 mg/dL = 0.357 mmol/L
                const conversionFactor = 0.357;
                
                if (fromUnit === 'mg/dL' && toUnit === 'mmol/L') {
                    return value * conversionFactor;
                } else if (fromUnit === 'mmol/L' && toUnit === 'mg/dL') {
                    return value / conversionFactor;
                }
                
                return value; // 如果单位相同
            }
        });
    </script>
</body>
</html>`;

const bmr = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMR计算器 - 基础代谢率计算</title>
    <style>
        :root {
        --primary-color: #4a6bff;
        --secondary-color: #f8f9fa;
        --text-color: #333;
        --light-text: #6c757d;
        --border-color: #dee2e6;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --accent-color: #ff7e5f;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: var(--dark-color);
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .description {
            text-align: center;
            margin-bottom: 30px;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border 0.3s;
        }
        
        input:focus, select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.2);
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .radio-option {
            display: flex;
            align-items: center;
        }
        
        .radio-option input {
            width: auto;
            margin-right: 8px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            transition: background-color 0.3s;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: var(--secondary-color);
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        
        .result.active {
            display: block;
            animation: fadeIn 0.5s;
        }
        
        .result h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .result-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--accent-color);
            margin: 10px 0;
        }
        
        .result-details {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .activity-levels {
            margin-top: 20px;
        }
        
        .activity-level {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-level:last-child {
            border-bottom: none;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基础代谢率（BMR）计算器</h1>
        <p class="description">基础代谢率(BMR)是指人体在清醒而又极端安静的状态下，不受肌肉活动、环境温度、食物及精神紧张等影响时的能量代谢率。</p>
        
        <div id="bmrForm">
            <div class="form-group">
                <label for="gender">性别</label>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="male" name="gender" value="male" checked>
                        <label for="male">男性</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="female" name="gender" value="female">
                        <label for="female">女性</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="age">年龄（岁）</label>
                <input type="number" id="age" min="15" max="120" required>
            </div>
            
            <div class="form-group">
                <label for="height">身高（厘米）</label>
                <input type="number" id="height" min="100" max="250" required>
            </div>
            
            <div class="form-group">
                <label for="weight">体重（公斤）</label>
                <input type="number" id="weight" min="30" max="300" step="0.1" required>
            </div>
            
            <button id="calculateBtn" type="button">计算BMR</button>
        </div>
        
        <div id="result" class="result">
            <h2>您的基础代谢率（BMR）</h2>
            <div class="result-value" id="bmrValue">0</div>
            <p>这是您每天在完全休息状态下维持基本生命功能所需的最低卡路里。</p>
            
            <div class="result-details">
                <h3>基于活动水平的每日热量需求</h3>
                <p>根据您的活动水平，您每天大约需要消耗的卡路里：</p>
                
                <div class="activity-levels">
                    <div class="activity-level">
                        <span>久坐（很少或没有运动）</span>
                        <span id="sedentary">0</span>
                    </div>
                    <div class="activity-level">
                        <span>轻度活动（每周1-3天运动）</span>
                        <span id="light">0</span>
                    </div>
                    <div class="activity-level">
                        <span>中度活动（每周3-5天运动）</span>
                        <span id="moderate">0</span>
                    </div>
                    <div class="activity-level">
                        <span>高度活动（每周6-7天运动）</span>
                        <span id="active">0</span>
                    </div>
                    <div class="activity-level">
                        <span>非常高度活动（体力劳动或每天训练）</span>
                        <span id="veryActive">0</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>注意：此计算结果仅供参考，实际代谢率可能因个体差异而有所不同。</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calculateBtn = document.getElementById('calculateBtn');
            
            calculateBtn.addEventListener('click', function() {
                // 获取输入值
                const gender = document.querySelector('input[name="gender"]:checked').value;
                const age = parseFloat(document.getElementById('age').value);
                const height = parseFloat(document.getElementById('height').value);
                const weight = parseFloat(document.getElementById('weight').value);
                
                // 验证输入
                if (isNaN(age) || isNaN(height) || isNaN(weight)) {
                    alert('请输入有效的数值');
                    return;
                }
                
                // 计算BMR
                let bmr;
                if (gender === 'male') {
                    bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
                } else {
                    bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
                }
                
                // 显示结果
                document.getElementById('bmrValue').textContent = Math.round(bmr) + ' 卡路里/天';
                
                // 计算不同活动水平的热量需求
                document.getElementById('sedentary').textContent = Math.round(bmr * 1.2) + ' 卡路里';
                document.getElementById('light').textContent = Math.round(bmr * 1.375) + ' 卡路里';
                document.getElementById('moderate').textContent = Math.round(bmr * 1.55) + ' 卡路里';
                document.getElementById('active').textContent = Math.round(bmr * 1.725) + ' 卡路里';
                document.getElementById('veryActive').textContent = Math.round(bmr * 1.9) + ' 卡路里';
                
                // 显示结果区域
                document.getElementById('result').classList.add('active');
            });
        });
    </script>
</body>
</html>`;
export const simulateLLMCall = (requirement) => {
    return new Promise((resolve) => {
        setTimeout(() => {
            // 根据不同的需求返回不同的组件代码
            if (requirement.includes('BMI')) {
                resolve({
                    code: `
                    <!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMI健康计算器</title>
    <style>
        :root {
            --primary-color: #4a6bff;
            --secondary-color: #f8f9fa;
            --text-color: #333;
            --light-text: #6c757d;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f7ff;
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            max-width: 500px;
            width: 100%;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            transition: all 0.3s ease;
        }
        
        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 25px;
            font-weight: 600;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }
        
        input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 16px;
            transition: border 0.3s ease;
        }
        
        input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 107, 255, 0.2);
        }
        
        .unit {
            color: var(--light-text);
            font-size: 14px;
            margin-left: 5px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            width: 100%;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #3a5bef;
            transform: translateY(-2px);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            background-color: var(--secondary-color);
            text-align: center;
            display: none;
        }
        
        .bmi-value {
            font-size: 36px;
            font-weight: 700;
            margin: 10px 0;
        }
        
        .bmi-category {
            font-size: 18px;
            font-weight: 500;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 10px;
        }
        
        .bmi-info {
            margin-top: 20px;
            font-size: 14px;
            color: var(--light-text);
        }
        
        .bmi-scale {
            margin-top: 25px;
            width: 100%;
            height: 10px;
            background: linear-gradient(to right, #dc3545, #ffc107, #28a745);
            border-radius: 5px;
            position: relative;
        }
        
        .bmi-indicator {
            position: absolute;
            top: -10px;
            width: 20px;
            height: 20px;
            background-color: white;
            border: 3px solid var(--primary-color);
            border-radius: 50%;
            transform: translateX(-50%);
            transition: left 0.5s ease;
        }
        
        .category-underweight {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .category-normal {
            background-color: #d4edda;
            color: #155724;
        }
        
        .category-overweight {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .category-obese {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        @media (max-width: 576px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BMI健康计算器</h1>
        
        <div class="input-group">
            <label for="height">身高 <span class="unit">(厘米)</span></label>
            <input type="number" id="height" placeholder="请输入您的身高" min="100" max="250">
        </div>
        
        <div class="input-group">
            <label for="weight">体重 <span class="unit">(公斤)</span></label>
            <input type="number" id="weight" placeholder="请输入您的体重" min="30" max="200">
        </div>
        
        <button id="calculate">计算BMI</button>
        
        <div class="result" id="result">
            <h3>您的BMI指数</h3>
            <div class="bmi-value" id="bmi-value">0.0</div>
            <div class="bmi-category" id="bmi-category">-</div>
            
            <div class="bmi-scale">
                <div class="bmi-indicator" id="bmi-indicator"></div>
            </div>
            
            <div class="bmi-info">
                BMI (身体质量指数) = 体重(kg) / (身高(m) × 身高(m))
            </div>
        </div>
    </div>

    <script>
        document.getElementById('calculate').addEventListener('click', function() {
            const height = parseFloat(document.getElementById('height').value) / 100; // 转换为米
            const weight = parseFloat(document.getElementById('weight').value);
            
            if (isNaN(height) || isNaN(weight) || height <= 0 || weight <= 0) {
                alert('请输入有效的身高和体重数值');
                return;
            }
            
            const bmi = weight / (height * height);
            const roundedBMI = bmi.toFixed(1);
            
            document.getElementById('bmi-value').textContent = roundedBMI;
            
            let category = '';
            let categoryClass = '';
            let indicatorPosition = 0;
            
            if (bmi < 18.5) {
                category = '体重过轻';
                categoryClass = 'category-underweight';
                indicatorPosition = (bmi / 18.5) * 25;
            } else if (bmi >= 18.5 && bmi < 24) {
                category = '正常范围';
                categoryClass = 'category-normal';
                indicatorPosition = 25 + ((bmi - 18.5) / (24 - 18.5)) * 50;
            } else if (bmi >= 24 && bmi < 28) {
                category = '超重';
                categoryClass = 'category-overweight';
                indicatorPosition = 75 + ((bmi - 24) / (28 - 24)) * 15;
            } else {
                category = '肥胖';
                categoryClass = 'category-obese';
                indicatorPosition = 90 + ((bmi - 28) / (40 - 28)) * 10;
                if (indicatorPosition > 100) indicatorPosition = 100;
            }
            
            const categoryElement = document.getElementById('bmi-category');
            categoryElement.textContent = category;
            categoryElement.className = 'bmi-category ' + categoryClass;
            
            document.getElementById('bmi-indicator').style.left = &#126;&#36;{indicatorPosition}%&#36;&#126;;
            
            document.getElementById('result').style.display = 'block';
        });
    </script>
</body>
</html>
`
                });
            } else if (requirement.includes('BMR')) {
                resolve({
                    code: bmr
                });
            } else if (requirement.includes('肾功能单位换算')) {
                resolve({
                    code: shen_gong_neng
                })
            }
        }, 500)
    })
}


