/**
 * 统一内容分析器 - 智能检测消息内容类型和格式
 * 支持流式内容的实时分析、Markdown检测和类型识别
 */

export class ContentAnalyzer {
  constructor() {
    this.buffer = ''
    this.detectedType = 'text'
    this.confidence = 0
    
    // Markdown 特征权重配置
    this.markdownFeatures = {
      // 高权重特征（强 Markdown 指示器）
      codeBlocks: { weight: 0.8, pattern: /```[\s\S]*?```/ },
      tables: { weight: 0.7, pattern: /\|.*\|.*\n.*[-:]+.*[-:]/ },
      headers: { weight: 0.6, pattern: /^#{1,6}\s+.+$/m },
      
      // 中等权重特征
      links: { weight: 0.4, pattern: /\[([^\]]+)\]\(([^)]+)\)/ },
      images: { weight: 0.4, pattern: /!\[([^\]]*)\]\(([^)]+)\)/ },
      boldText: { weight: 0.3, pattern: /\*\*[^*\n]+\*\*/ },
      italicText: { weight: 0.2, pattern: /\*[^*\n]+\*(?!\*)/ },
      inlineCode: { weight: 0.3, pattern: /`[^`\n]+`/ },
      
      // 低权重特征（需要组合判断）
      unorderedLists: { weight: 0.2, pattern: /^\s*[-*+]\s+.+$/m },
      orderedLists: { weight: 0.2, pattern: /^\s*\d+\.\s+.+$/m },
      blockquotes: { weight: 0.3, pattern: /^>\s+.+$/m },
      strikethrough: { weight: 0.2, pattern: /~~[^~\n]+~~/ },
      horizontalRules: { weight: 0.4, pattern: /^\s*[-*_]{3,}\s*$/m }
    }
    
    // Markdown 判定阈值
    this.markdownThreshold = 0.5
  }

  /**
   * 分析内容类型（支持流式和静态）
   * @param {string} content - 要分析的内容
   * @param {boolean} isIncremental - 是否为增量内容（流式）
   * @returns {Object} 分析结果
   */
  analyze(content, isIncremental = false) {
    if (isIncremental) {
      this.buffer += content
    } else {
      this.buffer = content
    }

    const analysis = this._performAnalysis()
    
    // 更新检测状态
    this.detectedType = analysis.type
    this.confidence = analysis.confidence

    return {
      ...analysis,
      buffer: this.buffer,
      isComplete: !isIncremental || analysis.confidence >= 0.8
    }
  }

  /**
   * 执行详细的内容分析
   * @private
   */
  _performAnalysis() {
    const content = this.buffer.trim()
    
    if (!content) {
      return { type: 'text', confidence: 0, features: [] }
    }

    // 分析各种内容特征
    const features = this._extractFeatures(content)
    
    // 根据特征确定内容类型和置信度
    return this._classifyContent(features, content)
  }

  /**
   * 提取内容特征
   * @private
   */
  _extractFeatures(content) {
    const features = {
      // 深度思考特征（最高优先级）
      hasThinkTags: /<think>[\s\S]*?<\/think>/.test(content),
      thinkTagStart: content.includes('<think>'),
      thinkTagEnd: content.includes('</think>'),
      
      // Markdown 特征
      hasHeaders: /^#{1,6}\s/.test(content) || /\n#{1,6}\s/.test(content),
      hasCodeBlocks: /```[\s\S]*?```/.test(content),
      hasCodeBlockStart: /```\w*\n/.test(content),
      hasCodeBlockEnd: content.includes('```') && content.split('```').length > 2,
      hasInlineCode: /`[^`\n]+`/.test(content),
      hasList: /^[\s]*[-*+]\s/.test(content) || /\n[\s]*[-*+]\s/.test(content),
      hasOrderedList: /^[\s]*\d+\.\s/.test(content) || /\n[\s]*\d+\.\s/.test(content),
      hasLinks: /\[([^\]]+)\]\(([^)]+)\)/.test(content),
      hasImages: /!\[([^\]]*)\]\(([^)]+)\)/.test(content),
      hasBlockquote: /^>/.test(content) || /\n>/.test(content),
      hasTables: /\|.*\|/.test(content) && /[-:]+/.test(content),
      hasBoldItalic: /\*\*.*\*\*/.test(content) || /__.*__/.test(content) || /\*.*\*/.test(content),
      hasStrikethrough: /~~.*~~/.test(content),
      hasHorizontalRules: /^\s*[-*_]{3,}\s*$/m.test(content),
      
      // 代码特征（独立代码块）
      hasOnlyCode: this._isOnlyCode(content),
      codeLanguage: this._detectCodeLanguage(content),
      
      // 文本特征
      wordCount: content.split(/\s+/).length,
      hasSpecialChars: /[{}[\]()#*`_~]/.test(content),
      lineCount: content.split('\n').length,
      
      // 数学公式特征
      hasMathInline: /\$[^$\n]+\$/.test(content),
      hasMathBlock: /\$\$[\s\S]*?\$\$/.test(content)
    }
    
    return features
  }

  /**
   * 基于特征分类内容
   * @private
   */
  _classifyContent(features, content) {
    // 深度思考类型检测（最高优先级）
    if (features.hasThinkTags) {
      return {
        type: 'deep-thinking',
        confidence: 0.95,
        features: ['think-tags'],
        subType: this._analyzeThinkingContent(content),
        isMarkdown: false
      }
    }
    
    if (features.thinkTagStart && !features.thinkTagEnd && this.buffer.length > 10) {
      return {
        type: 'deep-thinking',
        confidence: 0.8,
        features: ['partial-think-tags'],
        subType: 'streaming',
        isMarkdown: false
      }
    }

    // Markdown 类型检测
    const markdownAnalysis = this._analyzeMarkdownFeatures(features)
    if (markdownAnalysis.isMarkdown) {
      return {
        type: 'markdown',
        confidence: markdownAnalysis.score,
        features: markdownAnalysis.features,
        subType: markdownAnalysis.subType,
        isMarkdown: true
      }
    }

    // 纯代码检测
    if (features.hasOnlyCode || (features.hasCodeBlocks && features.wordCount < 20)) {
      return {
        type: 'code',
        confidence: 0.85,
        features: ['code-only'],
        language: features.codeLanguage,
        isMarkdown: true // 代码也用 Markdown 渲染
      }
    }

    // 默认文本类型
    return {
      type: 'text',
      confidence: 0.7,
      features: ['plain-text'],
      subType: features.wordCount > 100 ? 'long-text' : 'short-text',
      isMarkdown: false
    }
  }

  /**
   * 分析 Markdown 特征
   * @private
   */
  _analyzeMarkdownFeatures(features) {
    let score = 0
    const detectedFeatures = []
    
    // 计算 Markdown 评分
    for (const [featureName, config] of Object.entries(this.markdownFeatures)) {
      const featureKey = this._getFeatureKey(featureName)
      if (features[featureKey]) {
        detectedFeatures.push(featureName)
        score += config.weight
      }
    }

    // 特殊规则调整
    const adjustedScore = this._applyMarkdownRules(score, features, detectedFeatures)
    
    return {
      isMarkdown: adjustedScore >= this.markdownThreshold,
      score: Math.min(1.0, adjustedScore),
      features: detectedFeatures,
      subType: this._getMarkdownSubType(detectedFeatures)
    }
  }

  /**
   * 获取特征键名映射
   * @private
   */
  _getFeatureKey(featureName) {
    const keyMap = {
      'codeBlocks': 'hasCodeBlocks',
      'tables': 'hasTables',
      'headers': 'hasHeaders',
      'links': 'hasLinks',
      'images': 'hasImages',
      'boldText': 'hasBoldItalic',
      'italicText': 'hasBoldItalic',
      'inlineCode': 'hasInlineCode',
      'unorderedLists': 'hasList',
      'orderedLists': 'hasOrderedList',
      'blockquotes': 'hasBlockquote',
      'strikethrough': 'hasStrikethrough',
      'horizontalRules': 'hasHorizontalRules'
    }
    return keyMap[featureName] || featureName
  }

  /**
   * 应用 Markdown 特殊规则
   * @private
   */
  _applyMarkdownRules(score, features, detectedFeatures) {
    let multiplier = 1
    
    // 规则1：纯文本 + 少量粗体/斜体 = 降权重
    if (detectedFeatures.length <= 2 && 
        detectedFeatures.every(f => ['boldText', 'italicText', 'inlineCode'].includes(f))) {
      const formatRatio = this._calculateFormatRatio(this.buffer)
      if (formatRatio < 0.1) { // 格式化内容占比少于10%
        multiplier *= 0.3
      }
    }

    // 规则2：只有列表但内容简单 = 降权重  
    if (detectedFeatures.length === 1 && 
        ['unorderedLists', 'orderedLists'].includes(detectedFeatures[0])) {
      const lines = this.buffer.split('\n').filter(line => line.trim())
      if (lines.length <= 3) { // 少于3行列表
        multiplier *= 0.4
      }
    }

    // 规则3：包含代码块/表格/标题 = 强制 Markdown
    if (detectedFeatures.some(f => ['codeBlocks', 'tables', 'headers'].includes(f))) {
      multiplier = Math.max(multiplier, 2.0)
    }

    // 规则4：长文本 + 多种格式 = 提升权重
    if (this.buffer.length > 200 && detectedFeatures.length >= 3) {
      multiplier *= 1.5
    }

    return score * multiplier
  }

  /**
   * 计算格式化内容占比
   * @private
   */
  _calculateFormatRatio(content) {
    const formatPatterns = [
      /\*\*[^*\n]+\*\*/g,
      /\*[^*\n]+\*/g, 
      /`[^`\n]+`/g,
      /~~[^~\n]+~~/g
    ]
    
    let formatLength = 0
    formatPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      formatLength += matches.join('').length
    })
    
    return formatLength / content.length
  }

  /**
   * 获取 Markdown 子类型
   * @private
   */
  _getMarkdownSubType(features) {
    if (features.includes('codeBlocks') && features.includes('unorderedLists')) return 'technical-doc'
    if (features.includes('headers') && features.includes('unorderedLists')) return 'structured-doc'
    if (features.includes('tables')) return 'data-doc'
    if (features.includes('codeBlocks')) return 'code-doc'
    return 'general'
  }

  /**
   * 分析思考内容
   * @private
   */
  _analyzeThinkingContent(content) {
    const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/)
    if (thinkMatch) {
      const thinkingPart = thinkMatch[1].trim()
      const answerPart = content.replace(/<think>[\s\S]*?<\/think>/, '').trim()
      
      return {
        type: 'complete',
        hasAnswer: answerPart.length > 0,
        answerType: answerPart ? this.analyze(answerPart, false).type : 'none'
      }
    }
    
    return { type: 'streaming' }
  }

  /**
   * 检测是否为纯代码
   * @private
   */
  _isOnlyCode(content) {
    // 移除代码块标记
    const withoutCodeBlocks = content.replace(/```[\s\S]*?```/g, '')
    const withoutInlineCode = withoutCodeBlocks.replace(/`[^`]+`/g, '')
    
    // 检查剩余内容是否很少
    const remainingText = withoutInlineCode.replace(/\s+/g, ' ').trim()
    return remainingText.length < 20 && content.includes('```')
  }

  /**
   * 检测代码语言
   * @private
   */
  _detectCodeLanguage(content) {
    const codeBlockMatch = content.match(/```(\w+)/)
    if (codeBlockMatch) {
      return codeBlockMatch[1].toLowerCase()
    }
    
    // 基于内容特征推测语言
    if (/import\s+.*from|export\s+.*{/.test(content)) return 'javascript'
    if (/def\s+\w+\(|import\s+\w+/.test(content)) return 'python'
    if (/<\w+.*>|<\/\w+>/.test(content)) return 'html'
    if (/\.(css|scss|sass)|\{[\s\S]*?\}/.test(content)) return 'css'
    
    return 'unknown'
  }

  /**
   * 快速检测（简化版）
   */
  quickCheck(content) {
    if (!content) return false
    
    // 快速检测强特征
    const strongPatterns = [
      /```[\s\S]*?```/,     // 代码块
      /\|.*\|.*\n.*[-:]/,   // 表格
      /^#{1,6}\s+/m,        // 标题
      /\[([^\]]+)\]\(([^)]+)\)/ // 链接
    ]
    
    return strongPatterns.some(pattern => pattern.test(content))
  }

  /**
   * 重置分析器状态
   */
  reset() {
    this.buffer = ''
    this.detectedType = 'text'
    this.confidence = 0
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      buffer: this.buffer,
      detectedType: this.detectedType,
      confidence: this.confidence
    }
  }
}

// 导出单例实例
export const contentAnalyzer = new ContentAnalyzer()

// 快捷分析函数
export function analyzeContent(content, isIncremental = false) {
  return contentAnalyzer.analyze(content, isIncremental)
}

// 检测内容类型的快捷函数
export function detectContentType(content) {
  const result = analyzeContent(content, false)
  return result.type
}

// 检测是否应该使用 Markdown 渲染器
export function shouldUseMarkdown(content) {
  const analysis = analyzeContent(content, false)
  return analysis.isMarkdown || analysis.type === 'markdown' || analysis.type === 'code'
}

// 快速 Markdown 检测
export function quickMarkdownCheck(content) {
  return contentAnalyzer.quickCheck(content)
}

// 向后兼容的别名
export const markdownDetector = contentAnalyzer
export const shouldUseMarkdownRenderer = shouldUseMarkdown