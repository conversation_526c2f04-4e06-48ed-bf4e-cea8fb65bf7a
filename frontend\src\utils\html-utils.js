// html-utils.js
import { h } from 'vue'
import DOMPurify from 'dompurify';


// 配置 DOMPurify 默认选项
const defaultPurifyOptions = {
    ALLOWED_TAGS: [
        'div', 'p', 'span', 'a', 'img', 'br', 'hr',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li', 'strong', 'em', 'b', 'i', 'u',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'blockquote', 'pre', 'code'
    ],
    ALLOWED_ATTR: [
        'class', 'style', 'href', 'src', 'alt', 'title',
        'target', 'width', 'height', 'border', 'colspan', 'rowspan',
        'align', 'valign'
    ],
    FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover']
};

function compressHTML(html) {
    // 去除注释
    let compressedHtml = html.replace(/<!--[\s\S]*?-->/g, "");
    // 去除多余空格
    compressedHtml = compressedHtml.replace(/\s+/g, " ");
    // 去除标签之间的空格
    compressedHtml = compressedHtml.replace(/> +</g, "><");
    return compressedHtml.trim();
}

/**
 * 转义 Vue 模板冲突字符
 * @param {string} html - 原始HTML字符串
 * @returns {string} 转义后的安全字符串
 */
export function escapeVueChars(html) {
    if (!html) return '';

    return html
        .replace(/`/g, '&#126;')
        .replace(/\$/g, '&#36;')         // 转义$
        .replace(/\{\{/g, '&#123;&#123;') // 转义{{
        .replace(/\}\}/g, '&#125;&#125;') // 转义}}
        .replace(/v-bind:/g, 'v-&#98;ind:') // 转义v-bind
        .replace(/v-on:/g, 'v-&#111;n:')  // 转义v-on
        .replace(/v-model/g, 'v-&#109;odel') // 转义v-model
        .replace(/v-for/g, 'v-&#102;or'); // 转义v-for
}

/**
 * 还原被转义的 Vue 模板字符
 * @param {string} html - 转义后的HTML字符串
 * @returns {string} 还原后的原始字符串
 */
export function unescapeVueChars(html) {
    if (!html) return '';

    let unescapeHtml = html
        .replace(/&#36;/g, '$')          // 还原$
        .replace(/&#126;/g, '`')          // 还原$
        .replace(/&#123;&#123;/g, '{{')  // 还原{{
        .replace(/&#125;&#125;/g, '}}')  // 还原}}
        .replace(/v-&#98;ind:/g, 'v-bind:') // 还原v-bind
        .replace(/v-&#111;n:/g, 'v-on:')  // 还原v-on
        .replace(/v-&#109;odel/g, 'v-model') // 还原v-model
        .replace(/v-&#102;or/g, 'v-for'); // 还原v-for

    return unescapeHtml;
}


/**
 * 安全净化HTML内容
 * @param {string} html - 原始HTML
 * @param {object} options - DOMPurify配置选项
 * @returns {string} 净化后的HTML
 */
export function sanitizeHtml(html, options = {}) {
    const mergedOptions = { ...defaultPurifyOptions, ...options };
    return DOMPurify.sanitize(html, mergedOptions);
}

/**
 * 处理带变量的HTML模板
 * @param {string} template - 包含{{变量}}的模板
 * @param {object} data - 变量数据
 * @param {object} purifyOptions - DOMPurify配置选项
 * @returns {string} 转义后的安全HTML
 */
export function renderTemplate(template, data = {}, purifyOptions = {}) {
    // 先渲染变量
    let rendered = template.replace(/\{\{\s*(\w+)\s*\}\}/g, (match, key) => {
        return data[key] || '';
    });

    // 然后转义剩余Vue特殊字符
    const escaped = escapeVueChars(rendered);

    // 最后净化HTML
    return sanitizeHtml(escaped, purifyOptions);
}




export default {
    escapeVueChars,
    unescapeVueChars,
    sanitizeHtml,
    renderTemplate,
};