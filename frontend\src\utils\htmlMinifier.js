// import { minify } from 'html-minifier-terser';

// /**
//  * 压缩HTML代码
//  * @param {string} html - 需要压缩的HTML字符串
//  * @param {object} [options] - 压缩选项
//  * @returns {Promise<string>} 压缩后的HTML
//  */
// export async function compressHTML(html, options) {
//   const defaultOptions = {
//     collapseWhitespace: true,       // 折叠空白
//     removeComments: true,           // 移除注释
//     removeRedundantAttributes: true, // 移除冗余属性
//     removeScriptTypeAttributes: true,// 移除script的type属性
//     removeStyleLinkTypeAttributes: true, // 移除style/link的type属性
//     useShortDoctype: true,          // 使用短的doctype
//     minifyJS: true,                 // 压缩内联JS
//     minifyCSS: true                 // 压缩内联CSS
//   };

//   try {
//     return await minify(html, { ...defaultOptions, ...options });
//   } catch (error) {
//     console.error('HTML压缩失败:', error);
//     return html; // 压缩失败返回原内容
//   }
// }