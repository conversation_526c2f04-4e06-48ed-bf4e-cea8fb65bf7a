//定制请求的实例

//导入axios  npm install axios
import axios from 'axios';
import { ElMessage } from "element-plus";
import { useTokenStore } from "@/stores/token.js"
import { getBaseAPI } from "@/utils/env.js"
//定义一个变量,记录公共的前缀  ,  baseURL
const baseURL = getBaseAPI()
const instance = axios.create({ baseURL })
import router from '@/router'

//请求拦截器
instance.interceptors.request.use(
    (config) => {
        const tokenStore = useTokenStore();
        if (tokenStore.token) {
            config.headers.setAuthorization(tokenStore.token)
        }
        return config;


    },
    (err) => {
        Promise.reject(err)
    }
)

//添加响应拦截器
instance.interceptors.response.use(
    result => {
        if (result.data.code === 0) {
            return result.data
        }
        ElMessage.error(result.data.message ? result.data.message : "服务异常")
        return Promise.reject((result.data))

    },
    err => {
        if (err.response.status === 401 || err.response.status_code === 401 ) {
            ElMessage.error('请先登录')
            router.push('/login')
        }

        else {
            ElMessage.error('服务异常')
        }

        return Promise.reject(err);//异步的状态转化成失败的状态
    }
)

export default instance;