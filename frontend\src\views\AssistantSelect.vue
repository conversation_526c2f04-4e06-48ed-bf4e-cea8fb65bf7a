<template>
  <div class="assistant-select-container">
    <div class="selector-header">
      <h2>{{ $t('assistants.selectTitle') }}</h2>
    </div>
    
    <!-- 新的卡片式布局 -->
    <div class="assistant-cards-grid">
      <div 
        v-for="assistant in assistantTypes" 
        :key="assistant.id"
        class="assistant-card"
        @click="handleAssistantSelect(assistant)"
      >
        <div class="assistant-avatar">{{ assistant.avatar }}</div>
        <div class="assistant-content">
          <h3 class="assistant-title">{{ assistant.label }}</h3>
          <p class="assistant-description">{{ assistant.description }}</p>
          
          <div class="assistant-capabilities">
            <span 
              v-for="capability in assistant.capabilities" 
              :key="capability"
              class="capability-tag"
            >
              {{ capability }}
            </span>
          </div>
          
          <div class="assistant-tags">
            <span 
              v-for="tag in assistant.tags" 
              :key="tag"
              class="tag"
            >
              #{{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>
   
      
    <!-- 原来的Prompts组件布局 -->
    <!-- <div class="assistant-prompts-grid">
      <Prompts
        :items="assistantTypes"
        wrap
        @item-click="handleAssistantSelect"
      />
    </div> -->
  </div>
</template>

<script>
import { computed, ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { useAssistantStore } from "../stores/assistant";
import { Prompts } from "vue-element-plus-x";
import { ElMessage } from "element-plus";
import { assistantAPI } from "@/api";

export default {
  name: "AssistantSelect",
  components: {
    Prompts,
  },
  setup() {
    const { t } = useI18n();
    const router = useRouter();
    const assistantStore = useAssistantStore();
    const loading = ref(false);
    const assistantTypes = ref([]);

    // 加载助手列表
    const loadAssistants = async () => {
      loading.value = true;
      try {
        const assistants = await assistantAPI.getAssistants();
        
        // 缓存到Store中
        assistantStore.setAssistantsList(assistants);
        
        // 转换为组件所需的格式
        assistantTypes.value = assistants.map(assistant => ({
          key: assistant.id,
          label: assistant.label,
          description: assistant.description,
          avatar: assistant.avatar,
          specialty: assistant.specialty,
          capabilities: assistant.capabilities || [],
          tags: assistant.tags || [],
          id: assistant.id // 保留原始ID
        }));
      } catch (error) {
        console.error('Failed to load assistants:', error);
        ElMessage.error('加载助手列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 助手选择处理
    const handleAssistantSelect = (assistant) => {
      // 构造完整的助手对象，包含所有字段
      const selectedAssistant = {
        id: assistant.id,
        label: assistant.label,
        description: assistant.description,
        avatar: assistant.avatar,
        specialty: assistant.specialty,
        capabilities: assistant.capabilities || [],
        tags: assistant.tags || [],
        createdAt: assistant.createdAt,
        isActive: assistant.isActive
      };
      
      assistantStore.setAssistant(selectedAssistant);
      ElMessage.success(`已选择：${assistant.label}`);
      
      // 跳转到聊天页面
      router.push('/chat');
    };

    // 组件挂载时加载助手列表
    onMounted(() => {
      loadAssistants();
    });

    return {
      assistantTypes,
      loading,
      handleAssistantSelect,
    };
  },
};
</script>

<style scoped lang="scss">
.assistant-select-container {
  background-color: white;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: calc(100vh - 60px);
  justify-content: center;
}

.selector-header {
  text-align: center;
  margin-bottom: 40px;
}

.selector-header h2 {
  margin: 0 0 16px;
  color: #202124;
  font-size: 28px;
  font-weight: 600;
}

/* 原来的Prompts组件样式 */
.assistant-prompts-grid {
  width: 100%;
  padding: 0 100px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  justify-content: center;
  align-items: center;

  :deep(.el-prompts-items) {
      align-content: flex-start;
  }
 
  :deep(.el-prompts-item) {
    width: 30%;
    height: 150px;
  }
}

/* 新的卡片式布局样式 */
.assistant-cards-grid {
  width: 100%;
  max-width: 1200px;
  padding: 0 40px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  justify-content: center;
}

/* 新的卡片式布局相关样式 */
.assistant-card {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  min-height: 200px;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 8px 32px rgba(64, 158, 255, 0.1);
    transform: translateY(-2px);
  }
}

.assistant-avatar {
  font-size: 48px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 50%;
  margin-bottom: 8px;
}

.assistant-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.assistant-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.4;
}

.assistant-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

.assistant-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.capability-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.assistant-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.tag {
  background: #f4f4f5;
  color: #909399;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}
</style>