<template>
  <div class="chat-container">
    <!-- 当前助手选择 -->

    <!-- 聊天界面 -->
    <div class="chat-content">
      <div class="messages-container">
        <!-- 欢迎界面 - 新对话或无消息时显示 -->
        <div v-if="showWelcome" class="welcome-container">
          <WelcomePanel @card-click="handleWelcomeCardClick" />
        </div>

        <!-- 消息列表 -->
        <div v-else class="bubble-container">
          <BubbleList
            ref="bubbleListRef"
            :list="messages"
            max-height="100%"
            :always-show-scrollbar="false"
            :show-back-button="true"
            :btn-loading="true"
            :btn-color="'#409EFF'"
          >
            <!-- 自定义头部显示用户名/助手名 -->
            <template #header="{ item }">
              <div class="bubble-header">
                <strong>{{
                  item.role === "human"
                    ? $t("chat.you")
                    : selectedAssistant?.label || $t("chat.assistant")
                }}</strong>
                <!-- 只在助手回复时显示深度思考标签 -->
                <span v-if="item.deepThinking && item.role === 'ai'" class="deep-thinking-badge">
                  <el-icon><ElementPlus /></el-icon>
                  深度思考
                </span>
              </div>
            </template>
            
            <!-- 自定义内容区域，支持思考过程分离 -->
            <template #content="{ item }">
              <div v-if="item.deepThinking && item.role === 'ai'" class="deep-thinking-content">
                <!-- 思考过程展示区域 - 使用独立组件 -->
                <ThinkingProcess
                  :thinking="item.parsedContent?.thinking"
                  :show-thinking="item.showThinking"
                  custom-class="chat-thinking"
                  @update:showThinking="item.showThinking = $event"
                />
                
                <!-- 最终回答区域 -->
                <div v-if="item.parsedContent?.answer" class="final-answer">
                  <div class="answer-header">
                    <el-icon class="answer-icon"><ChatDotSquare /></el-icon>
                    <span>回答</span>
                  </div>
                  <div class="answer-content">
                    <!-- 统一使用 Markdown 渲染器 -->
                    <MarkdownRenderer
                      :content="item.parsedContent.answer"
                      :is-streaming="false"
                      :show-cursor="false"
                      custom-class="deep-thinking-answer"
                      @render-complete="handleRenderComplete(item)"
                      @code-copy="handleCodeCopy"
                    />
                  </div>
                </div>
              </div>
              
              <!-- 非深度思考模式 - 统一渲染 -->
              <template v-else>
                <!-- 助手回复统一使用 Markdown 渲染器 -->
                <MarkdownRenderer
                  v-if="item.role === 'ai'"
                  :content="item.content"
                  :is-streaming="item.typing"
                  :show-cursor="item.typing"
                  :custom-class="getContentClass(item)"
                  @render-complete="handleRenderComplete(item)"
                  @code-copy="handleCodeCopy"
                />
                
                <!-- 用户输入使用纯文本渲染器 -->
                <TextRenderer
                  v-else
                  :content="item.content"
                  :is-streaming="item.typing"
                  :show-cursor="false"
                  :custom-class="getContentClass(item)"
                  @render-complete="handleRenderComplete(item)"
                />
              </template>
            </template>
          </BubbleList>
        </div>
      </div>

      <div class="input-container">
        <Sender
          v-model="userInput"
          variant="updown"
          :auto-size="{ minRows: 2, maxRows: 5 }"
          clearable
          allow-speech
          
          :placeholder="isStreaming ? (isSelect ? '正在深度思考中...' : '正在生成回复...') : $t('chat.placeholder')"
          @send="sendMessage"
          @submit="sendMessage"
          @enter="sendMessage"
          @click-send="sendMessage"
          @keydown.enter.prevent="handleEnterKey"
        >
          <template #prefix>
            <div
              style="
                display: flex;
                align-items: center;
                gap: 8px;
                flex-wrap: wrap;
              "
            >
              <el-button round plain color="#626aef" :disabled="isStreaming">
                <el-icon><Paperclip /></el-icon>
              </el-button>

              <div
                v-if="!isStreaming"
                :class="{ isSelect }"
                style="
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  padding: 6px 12px;
                  border: 1px solid silver;
                  border-radius: 15px;
                  cursor: pointer;
                  font-size: 12px;
                "
                @click="isSelect = !isSelect"
              >
                <el-icon><ElementPlus /></el-icon>
                <span>深度思考</span>
              </div>

              <el-button 
                v-if="isStreaming" 
                round 
                plain 
                type="danger" 
                size="small"
                @click="cancelStream"
              >
                停止生成
              </el-button>
            </div>
          </template>

          <!-- <template #action-list>
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-button round color="#626aef" @click="sendMessage">
                <el-icon><Promotion /></el-icon>
              </el-button>
            </div>
          </template> -->
        </Sender>
      </div>
    </div>

  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { useUserInfoStore } from '@/stores/userInfo'
import { useAssistantStore } from "@/stores/assistant";
import { useConversationStore } from "@/stores/conversation";
import {
  Position,
  ArrowDown,
  ArrowUp,
  Paperclip,
  ElementPlus,
  Promotion,
  Operation,
  ChatDotSquare,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { BubbleList, Welcome, Sender } from "vue-element-plus-x";
import { conversationAPI, chatAPI } from "@/api";
import WelcomePanel from "@/components/WelcomePanel.vue";
import MarkdownRenderer from "@/components/MarkdownRenderer.vue";
import TextRenderer from "@/components/TextRenderer.vue";
import ThinkingProcess from "@/components/ThinkingProcess.vue";

export default {
  name: "Chat",
  components: {
    Position,
    ArrowDown,
    ArrowUp,
    Paperclip,
    ElementPlus,
    Promotion,
    Operation,
    ChatDotSquare,
    BubbleList,
    Welcome,
    Sender,
    WelcomePanel,
    MarkdownRenderer,
    TextRenderer,
    ThinkingProcess,
  },
  setup() {
    const { t } = useI18n();
    const router = useRouter();
    const route = useRoute();
    const userStore = useUserInfoStore()
    const assistantStore = useAssistantStore();
    const conversationStore = useConversationStore();
    const userInput = ref("");
    const inputEl = ref(null);
    const bubbleListRef = ref(null);
    const isSelect = ref(false);
    const isStreaming = ref(false);
    const currentStreamCancel = ref(null);
    const currentSessionId = ref(null);
    const currentConversationId = ref(null);

    const userAvatar = ref(
      "https://avatars.githubusercontent.com/u/76239030?v=4"
    );
    const assistantAvatar = ref(
      "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
    );


    // 消息列表
    const messages = ref([]);

    const selectedAssistant = computed(() => assistantStore.selectedAssistant);

    // 当前助手的 key，用于下拉框绑定
    const currentAssistantKey = computed({
      get: () => selectedAssistant.value?.id || selectedAssistant.value?.key,
      set: (value) => {
        // 这里通过 handleAssistantChange 处理
      },
    });

    // 是否显示欢迎界面
    const showWelcome = computed(() => {
      // 没有消息或只有系统欢迎消息时显示欢迎界面
      return (
        messages.value.length === 0 ||
        (messages.value.length === 1 &&
          messages.value[0].role === "ai" &&
          messages.value[0].isWelcome)
      );
    });


    // 滚动到底部
    const scrollToBottom = async () => {
      await nextTick();
      if (bubbleListRef.value) {
        bubbleListRef.value.scrollToBottom();
      }
    };

    // 重置聊天界面
    const resetChatInterface = () => {
      // 清空消息列表，显示欢迎界面
      messages.value = [];
    };

    // 解析深度思考内容，分离思考过程和最终回答
    const parseDeepThinkingContent = (content) => {
      if (!content) return null;
      
      const thinkRegex = /<think>([\s\S]*?)<\/think>/;
      const thinkMatch = content.match(thinkRegex);
      
      if (thinkMatch) {
        const thinking = thinkMatch[1].trim();
        const answer = content.replace(thinkRegex, '').trim();
        console.log('解析深度思考内容:', { thinking: thinking.substring(0, 100), answer: answer.substring(0, 100) });
        return { thinking, answer };
      }
      
      // 如果没有思考标签，但是深度思考模式，显示原内容作为思考过程
      return { thinking: content || '正在思考中...', answer: '' };
    };

    // 加载对话历史
    const loadConversationHistory = async (conversationId) => {
      if (!conversationId) {
        resetChatInterface();
        return;
      }
      const assistantId = selectedAssistant.value.id;
      
      try {
        // 临时使用假数据测试 Markdown 渲染
        const fakeMessagesData = [
          {
            type: 'human',
            data: { content: '能展示一下所有Markdown格式的渲染效果吗？我想测试一下组件是否能正确处理各种格式。' },
            deep_thinking: false
          },
          {
            type: 'ai', 
            data: { 
              content: '# 一级标题\n## 二级标题\n### 三级标题\n#### 四级标题\n##### 五级标题\n###### 六级标题\n\n**这是粗体文本**  \n__这也是粗体文本__\n\n*这是斜体文本*  \n_这也是斜体文本_\n\n***这是粗斜体文本***\n\n~~这是带删除线的文本~~\n\n- 无序列表项1\n- 无序列表项2\n  - 子列表项2.1\n  - 子列表项2.2\n\n1. 有序列表项1\n2. 有序列表项2\n   1. 子列表项2.1\n   2. 子列表项2.2\n\n[Element-Plus-X](https://element-plus-x.com "Element-Plus-X")\n\n![示例图片](https://element-plus-x.com/logo.png "一张示例图")\n\n>这是一段引用文本\n>\n>> 这是嵌套的引用文本\n\n---\n\n| 姓名 | 年龄 | 职业 |\n| ---- | ---- | ---- |\n| 张三 | 25   | 工程师 |\n| 李四 | 30   | 设计师 |\n\n### 行内代码\n\n用 `Element-Plus-X` 表示行内代码\n\n### 代码块\n\n```javascript\nconst code = "Element-Plus-X";\nconsole.log(code);\n```\n\n### 行内公式\n$e^{i\\pi} + 1 = 0$\n\n### 块级公式\n$$\nF(\\omega) = \\int_{-\\infty}^{\\infty} f(t) e^{-i\\omega t} dt\n$$\n\n### mermaid 饼状图\n\n```mermaid\npie\n    "传媒及文化相关" : 35\n    "广告与市场营销" : 8\n    "游戏开发" : 15\n    "影视动画与特效" : 12\n    "互联网产品设计" : 10\n    "VR/AR开发" : 5\n    "其他" : 15\n```' 
            },
            deep_thinking: false
          },
          {
            type: 'human',
            data: { content: '能给我一个深度思考的例子吗？比如如何设计一个复杂的状态管理系统' },
            deep_thinking: false
          },
          {
            type: 'ai',
            data: { 
              content: `<think>
用户询问如何设计复杂的状态管理系统，这是一个很好的问题。我需要深度思考这个问题的几个方面：

1. **状态管理的核心问题**：
   - 状态的复杂性和规模
   - 多组件间的状态共享
   - 状态变更的可预测性
   - 时间旅行和调试能力
   - 异步操作的处理

2. **设计原则**：
   - 单一数据源
   - 状态只读
   - 使用纯函数修改状态
   - 可预测的状态变更

3. **架构考虑**：
   - 中央化 vs 分布式
   - 性能优化（避免不必要的重渲染）
   - 类型安全
   - 开发者体验

4. **实现方案**：
   - Redux/Zustand 模式
   - Observer 模式
   - Proxy-based 响应式系统
   - 分层状态管理

让我为用户提供一个全面的设计方案，包含代码示例和最佳实践。
</think>

# 复杂状态管理系统设计

## 核心设计原则

设计一个复杂的状态管理系统需要遵循以下核心原则：

### 1. 单一数据源 (Single Source of Truth)
所有的应用状态都存储在一个中央store中，确保数据的一致性。

### 2. 状态不可变 (Immutable State)
每次状态更新都返回新的状态对象，而不是修改现有状态。

### 3. 纯函数更新 (Pure Function Updates)
状态的变更通过纯函数进行，确保可预测性和可测试性。

## 系统架构设计

\`\`\`typescript
// 核心状态管理器接口
interface StateManager<T> {
  getState(): T;
  setState(updater: (state: T) => T): void;
  subscribe(listener: (state: T) => void): () => void;
  dispatch(action: Action): void;
}

// Action 定义
interface Action {
  type: string;
  payload?: any;
}

// 状态切片定义
interface StateSlice<T> {
  name: string;
  initialState: T;
  reducers: Record<string, (state: T, action: Action) => T>;
  selectors?: Record<string, (state: T) => any>;
}
\`\`\`

## 实现方案

### 主要状态管理器

\`\`\`typescript
class ComplexStateManager<T> implements StateManager<T> {
  private state: T;
  private listeners: Set<(state: T) => void> = new Set();
  private middleware: Middleware[] = [];
  
  constructor(initialState: T) {
    this.state = initialState;
  }
  
  getState(): T {
    return this.state;
  }
  
  setState(updater: (state: T) => T): void {
    const newState = updater(this.state);
    if (newState !== this.state) {
      this.state = newState;
      this.notifyListeners();
    }
  }
  
  subscribe(listener: (state: T) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
  
  dispatch(action: Action): void {
    // 应用中间件
    let finalAction = action;
    for (const middleware of this.middleware) {
      finalAction = middleware(finalAction, this.getState());
    }
    
    // 执行reducer
    this.setState(state => this.rootReducer(state, finalAction));
  }
  
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }
  
  private rootReducer(state: T, action: Action): T {
    // 组合多个reducer的逻辑
    return state;
  }
}
\`\`\`

### 中间件系统

\`\`\`typescript
type Middleware = (action: Action, state: any) => Action;

// 日志中间件
const loggerMiddleware: Middleware = (action, state) => {
  console.log('Action:', action);
  console.log('Current State:', state);
  return action;
};

// 异步处理中间件
const thunkMiddleware: Middleware = (action, state) => {
  if (typeof action === 'function') {
    return action(state);
  }
  return action;
};

// 持久化中间件
const persistMiddleware: Middleware = (action, state) => {
  if (action.meta?.persist) {
    localStorage.setItem('appState', JSON.stringify(state));
  }
  return action;
};
\`\`\`

### 选择器系统

\`\`\`typescript
// 记忆化选择器
class SelectorManager {
  private cache = new Map();
  
  createSelector<T, R>(
    selector: (state: T) => R,
    equalityFn = Object.is
  ) {
    return (state: T): R => {
      const cacheKey = this.getCacheKey(selector, state);
      
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (equalityFn(cached.input, state)) {
          return cached.output;
        }
      }
      
      const result = selector(state);
      this.cache.set(cacheKey, { input: state, output: result });
      return result;
    };
  }
  
  private getCacheKey(fn: Function, state: any): string {
    return \`\${fn.name}_\${JSON.stringify(state)}\`;
  }
}
\`\`\`

### React集成层

\`\`\`typescript
import { createContext, useContext, useState, useEffect } from 'react';

const StateContext = createContext<StateManager<any> | null>(null);

export function StateProvider({ children, store }) {
  return (
    <StateContext.Provider value={store}>
      {children}
    </StateContext.Provider>
  );
}

export function useStateManager<T>(): StateManager<T> {
  const context = useContext(StateContext);
  if (!context) {
    throw new Error('useStateManager must be used within StateProvider');
  }
  return context;
}

export function useSelector<T, R>(
  selector: (state: T) => R,
  equalityFn?: (a: R, b: R) => boolean
) {
  const store = useStateManager<T>();
  const [selectedState, setSelectedState] = useState(() => 
    selector(store.getState())
  );
  
  useEffect(() => {
    return store.subscribe((state) => {
      const newSelected = selector(state);
      if (!equalityFn || !equalityFn(selectedState, newSelected)) {
        setSelectedState(newSelected);
      }
    });
  }, [store, selector, selectedState, equalityFn]);
  
  return selectedState;
}
\`\`\`

## 最佳实践

### 1. 状态分割
将大的状态对象分割成小的、独立的切片：

\`\`\`typescript
interface AppState {
  user: UserState;
  ui: UIState;
  data: DataState;
  cache: CacheState;
}
\`\`\`

### 2. 性能优化
- 使用浅比较避免不必要的重渲染
- 实现选择器缓存
- 批量状态更新

### 3. 开发者工具
- 时间旅行调试
- 状态变更日志
- 性能监控

### 4. 类型安全
使用 TypeScript 确保类型安全，减少运行时错误。

## 总结

一个好的状态管理系统应该具备：

| 特性 | 说明 |
|------|------|
| **可预测性** | 相同输入产生相同输出 |
| **可调试性** | 完整的状态变更日志 |
| **性能** | 最小化不必要的重渲染 |
| **扩展性** | 支持插件和中间件 |
| **类型安全** | 编译时类型检查 |

这样的设计可以处理复杂应用的状态管理需求，同时保持代码的可维护性和可测试性。` 
            },
            deep_thinking: true
          },
          {
            type: 'human',
            data: { content: '这个设计很不错！能简单解释下中间件的执行顺序吗？' },
            deep_thinking: false
          },
          {
            type: 'ai',
            data: { 
              content: `非常好的测试！从刚才的渲染效果来看，我们的 MarkdownRenderer 组件已经能够正确处理：

✅ 各级标题格式
✅ 文本样式（粗体、斜体、删除线）
✅ 有序和无序列表（包括嵌套）
✅ 链接和图片
✅ 引用块（包括嵌套引用）
✅ 表格和对齐方式
✅ 行内代码和代码块
✅ 数学公式（如果支持 KaTeX）
✅ Mermaid 图表（如果启用插件）
✅ 分隔线
✅ 特殊字符转义
✅ 任务列表

组件能够智能地区分需要 Markdown 渲染的内容和普通文本，这样既保证了富文本的正确显示，又避免了对简单文本的过度处理。` 
            },
            deep_thinking: false
          },
          {
            type: 'human',
            data: { content: '谢谢你的测试！看起来渲染效果很好。' },
            deep_thinking: false
          },
          {
            type: 'ai',
            data: { 
              content: '很高兴能帮到你！现在我们已经成功实现了统一渲染器的方案。\n\n这样的好处是：\n- 所有AI回复都通过同一个Markdown渲染器\n- 纯文本也能得到很好的显示效果\n- 架构更简单，维护更容易\n- 用户体验更一致\n\n不管是复杂的Markdown格式还是简单的纯文本对话，都能完美显示！' 
            },
            deep_thinking: false
          },
          {
            type: 'human',
            data: { content: '能测试一下流式回复吗？' },
            deep_thinking: false
          },
          {
            type: 'ai',
            data: { 
              content: '当然可以！流式回复的特点是内容逐步显示，就像打字一样。\n\n现在统一使用MarkdownRenderer后，流式回复也能：\n✅ 实时渲染Markdown格式\n✅ 显示打字光标效果\n✅ 智能控制渲染速度\n✅ 完美处理纯文本内容\n\n```javascript\n// 流式渲染示例\nconst streamContent = "这是一段流式显示的内容...";\n```\n\n这就是现代AI助手的标准做法！' 
            },
            deep_thinking: false
          },
          {
            type: 'ai',
            data: { 
              content: '中间件的执行顺序很重要！让我用一个简单的例子说明：\n\n## 中间件执行流程\n\n假设我们有这样的中间件栈：\n\n```javascript\nconst middlewares = [\n  loggerMiddleware,    // 1\n  thunkMiddleware,     // 2\n  persistMiddleware    // 3\n];\n```\n\n当 dispatch 一个 action 时，执行顺序是：\n\n1. **loggerMiddleware** 首先执行 → 记录原始 action\n2. **thunkMiddleware** 接着执行 → 处理异步逻辑\n3. **persistMiddleware** 最后执行 → 持久化状态\n4. **reducer** 最终执行 → 更新状态\n\n## 实际执行示例\n\n```javascript\n// 当你 dispatch 这个 action：\ndispatch({ type: \'INCREMENT\', payload: 1 });\n\n// 执行流程：\n// 1. Logger: "Action received: INCREMENT"\n// 2. Thunk: "Action is not function, pass through"\n// 3. Persist: "Saving to localStorage..."\n// 4. Reducer: state.count = state.count + 1\n```\n\n这种 **洋葱模型** 确保每个中间件都能：\n- 在 action 到达 reducer 前进行预处理\n- 在状态更新后进行后处理\n- 修改或拦截 action\n\n简单来说：**先进后出**，就像洋葱一样一层层剥开！🧅' 
            },
            deep_thinking: false
          }
        ];

        // 转换消息格式为BubbleList所需的格式
        const messageHistory = fakeMessagesData.map((msg, index) => {
          // 优先使用后端保存的深度思考元数据，如果没有则通过内容检测
          const isDeepThinking = msg.deep_thinking || 
            (msg.type === 'ai' && msg.data?.content?.includes('<think>') && msg.data?.content?.includes('</think>'));
          
          const message = {
            key: index + 1,
            role: msg.type,
            content: msg.data? msg.data.content : "",
            placement: msg.type === 'human' ? 'end' : 'start',
            variant: msg.type === 'human' ? 'outlined' : 'filled',
            shape: 'corner',
            avatar: msg.type === 'human' ? userAvatar.value : assistantAvatar.value,
            avatarSize: '32px',
            avatarGap: '12px',
            isMarkdown: true,
            loading: false,
            typing: false,
            deepThinking: isDeepThinking,
            showThinking: false, // 历史消息默认折叠思考过程
          };
          
          // 如果是深度思考消息，解析内容
          if (isDeepThinking) {
            message.parsedContent = parseDeepThinkingContent(msg.data?.content || "");
            // 为深度思考消息添加特殊样式
            message.className = 'deep-thinking-bubble';
            message.maxWidth = '90%';
            message.minWidth = '400px';
          }
          
          return message;
        });

        messages.value = messageHistory;
        await scrollToBottom();
        
        // 注释掉真实的 API 调用，保留错误处理
        // const messagesData = await conversationAPI.getMessages(conversationId, assistantId);
      } catch (error) {
        console.error('Failed to load conversation history:', error);
        messages.value = [];
      }
    };

    // 监听路由变化
    watch(
      () => route.params.conversationId,
      (newConversationId) => {
        // 如果正在流式对话中，不要重新加载历史记录，避免覆盖正在进行的对话
        if (isStreaming.value && currentConversationId.value === newConversationId) {
          console.log('Streaming in progress, skipping history load for same conversation');
          return;
        }
        
        currentConversationId.value = newConversationId || null;
        if (newConversationId) {
          conversationStore.setCurrentConversation(newConversationId);
          loadConversationHistory(newConversationId);
        } else {
          conversationStore.resetCurrentConversation();
          resetChatInterface();
        }
      },
      { immediate: true }
    );


    // 发送消息
    const sendMessage = async () => {
      if (!userInput.value.trim()) return;
      if (isStreaming.value) return;
      if (!assistantStore.hasSelectedAssistant) {
        ElMessage.warning(t("assistants.pleaseSelectFirst"));
        return;
      }

      const userQuestion = userInput.value;
      userInput.value = "";
      isStreaming.value = true;

      // 检查是否需要创建新对话
      let conversationId = currentConversationId.value;
      if (!conversationId) {
        try {
          // 先创建会话
          const userId = userStore.userId;
          const newConversation = await conversationAPI.createConversation(assistantStore.selectedAssistant.id, userId);
          conversationId = newConversation.conversationId;
          
          // 更新当前对话状态
          currentConversationId.value = conversationId;
          conversationStore.setCurrentConversation(conversationId);
          
          // 更新URL
          router.replace(`/chat/${conversationId}`);
          
          // 触发会话历史更新事件
          window.dispatchEvent(new CustomEvent('conversationCreated', { 
            detail: { conversationId, assistantId: assistantStore.selectedAssistant?.id } 
          }));
          
        } catch (error) {
          console.error('Failed to create new conversation:', error);
          ElMessage.error('创建对话失败，请重试');
          isStreaming.value = false;
          return;
        }
      }

      // Add user message immediately to UI
      const userMessage = {
        key: messages.value.length + 1,
        role: "human",
        content: userQuestion,
        placement: "end",
        variant: "outlined",
        shape: "corner",
        avatar: userAvatar.value,
        avatarSize: "32px",
        avatarGap: "12px",
        isMarkdown: true,
        loading: false,
        typing: false,
        deepThinking: isSelect.value, // 标记是否为深度思考消息
      };
      messages.value.push(userMessage);
      await scrollToBottom();

      // 创建助手消息容器
      const assistantMessage = {
        key: messages.value.length + 1,
        role: "ai", 
        content: "加载中...",
        placement: "start",
        variant: "filled",
        shape: "corner",
        avatar: assistantAvatar.value,
        avatarSize: "32px",
        avatarGap: "12px",
        isMarkdown: true,
        loading: false,
        typing: true,
        deepThinking: isSelect.value, // 标记是否为深度思考回复
        showThinking: isSelect.value, // 深度思考模式默认展开思考过程
        // 深度思考模式的额外样式配置
        className: isSelect.value ? 'deep-thinking-bubble' : '',
        maxWidth: isSelect.value ? '90%' : '85%',
        minWidth: isSelect.value ? '400px' : '300px',
      };
      messages.value.push(assistantMessage);
      const assistantMessageIndex = messages.value.length - 1;
      await scrollToBottom();

      try {
        const userId = userStore.userId;
        // 调用流式API
        currentStreamCancel.value = chatAPI.sendStreamMessage(
          userQuestion,
          userId,
          // onMessage: 接收到内容片段
          (content) => {
            // 输出内容片段
            console.log("content", content);
            if(messages.value[assistantMessageIndex].content === '加载中...') {
              messages.value[assistantMessageIndex].content = '';
            }
            messages.value[assistantMessageIndex].content += content;
            
            // 如果是深度思考模式，实时解析内容
            if (isSelect.value) {
              const parsedContent = parseDeepThinkingContent(messages.value[assistantMessageIndex].content);
              messages.value[assistantMessageIndex].parsedContent = parsedContent;
              // 默认展开思考过程
              if (!messages.value[assistantMessageIndex].hasOwnProperty('showThinking')) {
                messages.value[assistantMessageIndex].showThinking = true;
              }
            }
            
            scrollToBottom();
          },
          // onError: 发生错误
          (error) => {
            console.error("流式请求错误:", error);
            messages.value[assistantMessageIndex].content = "抱歉，发生了错误，请重试。";
            messages.value[assistantMessageIndex].typing = false;
            ElMessage.error("发送消息失败：" + error);
            isStreaming.value = false;
            currentStreamCancel.value = null;
            currentSessionId.value = null;
          },
          // onComplete: 完成
          () => {
            messages.value[assistantMessageIndex].typing = false;
            isStreaming.value = false;
            currentStreamCancel.value = null;
            currentSessionId.value = null;
            scrollToBottom();
          },
          // onSessionId: 接收会话ID和对话ID
          (sessionId, streamConversationId) => {
            currentSessionId.value = sessionId;
            // 注意：现在conversationId已经在发送前创建，这里主要是更新sessionId
          },
          // 传递对话ID和助手ID（使用本地conversationId变量）
          conversationId,
          assistantStore.selectedAssistant?.id
        );
      } catch (error) {
        console.error("发送消息失败:", error);
        messages.value[assistantMessageIndex].content = "抱歉，发生了错误，请重试。";
        messages.value[assistantMessageIndex].typing = false;
        ElMessage.error("发送消息失败，请重试");
        isStreaming.value = false;
        currentStreamCancel.value = null;
      }
    };

    // 取消流式请求 - 混合方案
    const cancelStream = () => {
      if (currentStreamCancel.value && typeof currentStreamCancel.value === 'function') {
        try {
          // 执行混合取消：前端立即停止 + 后端停止推理
          currentStreamCancel.value();
          ElMessage.info('已取消生成');
        } catch (error) {
          console.error('Error calling cancel function:', error);
          ElMessage.error('取消请求时发生错误');
        }
        
        // 清理状态
        currentStreamCancel.value = null;
        currentSessionId.value = null;
        isStreaming.value = false;
      } else {
        ElMessage.warning('没有正在进行的请求可以取消');
        
        // 重置状态
        isStreaming.value = false;
        currentStreamCancel.value = null;
        currentSessionId.value = null;
      }
    };

    // 处理Enter键
    const handleEnterKey = (event) => {
      if (!event.shiftKey) {
        event.preventDefault();
        sendMessage();
      }
    };

    // 简单的markdown渲染（可以后续使用专业的markdown库）
    const renderMarkdown = (content) => {
      if (!content) return '';
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
    };

    
    // 获取内容样式类
    const getContentClass = (item) => {
      const classes = [];
      
      if (item.deepThinking) classes.push('deep-thinking-content');
      if (item.typing) classes.push('streaming-content');
      if (item.role === 'ai') classes.push('assistant-content');
      if (item.role === 'human') classes.push('user-content');
      
      return classes.join(' ');
    };
    
    // 处理渲染完成事件
    const handleRenderComplete = (item) => {
      console.log('Content render completed for item:', item.key);
    };
    

    // 处理代码复制事件
    const handleCodeCopy = ({ content, language }) => {
      console.log(`Code copied: ${language || 'unknown'} - ${content.length} chars`);
    };

    // 处理欢迎面板卡片点击
    const handleWelcomeCardClick = (cardData) => {
      // 直接将卡片的查询文本作为用户输入发送
      userInput.value = cardData.query;
      // 自动发送消息
      nextTick(() => {
        sendMessage();
      });
    };

    onMounted(async () => {
      // 检查是否已选择助手，如果没有则跳转到选择页面
      if (!selectedAssistant.value) {
        ElMessage.warning(t("assistants.pleaseSelectFirst"));
        router.push("/");
        return;
      }

      scrollToBottom();
      if (inputEl.value && inputEl.value.focus) {
        inputEl.value.focus();
      }
    });

    return {
      userInput,
      inputEl,
      bubbleListRef,
      messages,
      selectedAssistant,
      currentAssistantKey,
      showWelcome,
      isSelect,
      isStreaming,
      sendMessage,
      cancelStream,
      handleEnterKey,
      renderMarkdown,
      parseDeepThinkingContent,
      getContentClass,
      handleRenderComplete,
      handleCodeCopy,
      handleWelcomeCardClick,
    };
  },
};
</script>

<style scoped lang="scss">
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* 当前助手选择头部 */

/* 聊天内容区域 */
.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.messages-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.welcome-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-y: auto;
  min-height: 0;
  
}

.bubble-container {
  flex: 1;
  min-height: 0;
  padding: 16px;
}

/* 气泡消息宽度控制 */
:deep(.x-bubble-list) {
  max-width: 100%;
}

:deep(.x-bubble) {
  max-width: 85%;
  min-width: 300px;
  width: auto;
}

/* 深度思考消息的宽度优化 */
:deep(.deep-thinking-bubble) {
  max-width: 90% !important;
  min-width: 400px !important;
  width: 90% !important;
}

:deep(.deep-thinking-bubble .x-bubble__content) {
  width: 100%;
  max-width: none;
}

.bubble-header {
  margin-bottom: 4px;
  color: #606266;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.deep-thinking-badge {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  background: linear-gradient(135deg, #626aef 0%, #7c83f0 100%);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

/* 深度思考内容样式 */
.deep-thinking-content {
  margin: 0;
  width: 100%;
  max-width: 800px;
  min-width: 400px;
}

/* ThinkingProcess 组件在聊天中的样式 */
.chat-thinking {
  margin-bottom: 20px;
}

.final-answer {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  width: 100%;
  min-height: 60px; /* 确保回答区域有最小高度 */
}

.answer-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-bottom: 1px solid #e2e8f0;
}

.answer-icon {
  color: #4299e1;
  margin-right: 8px;
  font-size: 16px;
}

.answer-content {
  padding: 16px 20px;
  line-height: 1.7;
  color: #2d3748;
  font-size: 14px;
}

.answer-content code {
  background: #f7fafc;
  color: #d69e2e;
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #faf5e4;
}

.answer-content strong {
  color: #2b6cb0;
  font-weight: 600;
}

.answer-content h1, .answer-content h2, .answer-content h3 {
  color: #2d3748;
  margin-top: 20px;
  margin-bottom: 12px;
  font-weight: 600;
}

.answer-content ul, .answer-content ol {
  padding-left: 20px;
  margin: 12px 0;
}

.answer-content li {
  margin: 6px 0;
  line-height: 1.6;
}

/* 深度思考答案部分的专用样式 */
.deep-thinking-answer {
  width: 100%;
  
  :deep(.x-markdown) {
    line-height: 1.7;
    color: #2d3748;
    font-size: 14px;
    
    // 确保纯文本也有好的显示效果
    p {
      margin: 8px 0;
      line-height: 1.7;
    }
    
    // 优化代码显示
    code {
      background: #f7fafc;
      color: #d69e2e;
      padding: 3px 6px;
      border-radius: 4px;
      font-size: 13px;
    }
    
    // 优化标题显示
    h1, h2, h3, h4, h5, h6 {
      color: #2d3748;
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
    }
  }
}

.normal-content {
  line-height: 1.6;
}

.normal-content code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 13px;
  color: #e6a23c;
}

.input-container {
  border-top: 1px solid #e4e7ed;
  padding: 16px 20px;
  background-color: #ffffff;
  flex-shrink: 0;
}

/* 深度思考切换样式 */
.isSelect {
  background-color: #626aef !important;
  color: white !important;
  border-color: #626aef !important;
}

</style>