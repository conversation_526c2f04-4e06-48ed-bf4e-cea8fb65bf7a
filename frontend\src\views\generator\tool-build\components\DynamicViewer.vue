<template>
  <div class="dynamic-viewer">
    <!-- 组件模式 -->
    <component
      v-if="mode === 'component' && component"
      :is="component"
      v-bind="props"
      v-on="events"
    />
    
    <!-- HTML 模式 -->
    <!-- <div
      v-else-if="mode === 'html' && html"
      v-html="html"
      class="dynamic-viewer-html"
    /> 
     -->
    <!-- sandbox模式，限制同源和 allow-scripts允许执行脚本 -->
    <iframe  v-else-if="mode === 'html' && html" :srcdoc="html" id="iframeBox" class="dynamic-viewer-html" sandbox="allow-scripts allow-forms"></iframe>

    <!-- 空状态 -->
    <el-empty
      v-else
      :description="emptyText"
    />

    <!-- iframe隔离渲染 -->
     <!-- 普通渲染 -->
    <!-- <safe-html :html="html" /> -->
    
  </div>
</template>

<script setup>
import { defineProps, computed, shallowRef, onMounted, watch } from 'vue'
import { ElEmpty } from 'element-plus'

const props = defineProps({
  // 内容类型：'html' 或 'component'
  type: {
    type: String,
    required: true,
    validator: (value) => ['html', 'component'].includes(value)
  },
  // HTML 字符串（当 type='html' 时使用）
  html: {
    type: String,
    default: ''
  },
  // 组件定义（当 type='component' 时使用）
  component: {
    type: [Object, String, Promise],
    default: null
  },
  // 传递给组件的 props
  props: {
    type: Object,
    default: () => ({})
  },
  // 组件事件监听器
  events: {
    type: Object,
    default: () => ({})
  },
  // 空状态提示文本
  emptyText: {
    type: String,
    default: '暂无内容'
  },
  // 是否异步加载组件（仅对组件模式有效）
  async: {
    type: Boolean,
    default: false
  }
})

const mode = computed(() => props.type)
const resolvedComponent = shallowRef(null)

// 处理异步组件
const loadAsyncComponent = async () => {
  if (typeof props.component === 'function' || props.component instanceof Promise) {
    try {
      resolvedComponent.value = (await props.component()).default
    } catch (error) {
      console.error('Failed to load async component:', error)
      resolvedComponent.value = null
    }
  } else {
    resolvedComponent.value = props.component
  }
}

onMounted(() => {
  if (props.async && props.type === 'component') {
    loadAsyncComponent()
  }
})

watch(
  () => props.component,
  () => {
    if (props.async && props.type === 'component') {
      loadAsyncComponent()
    }
  }
)
</script>

<style scoped>

.dynamic-viewer {
  width: 100%;
  height: 100%;
}

.dynamic-viewer-html {
  width: 100%;
  height: calc(100% - 20px);
  overflow: auto;
  border: none;
}

</style>