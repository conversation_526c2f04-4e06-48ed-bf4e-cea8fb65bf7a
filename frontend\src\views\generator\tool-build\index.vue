<template>
  <div class="generator-container">
    <el-container>
      <!-- 左侧配置区域 -->
      <el-aside width="400px" class="config-panel">
        <div class="config-section">
          <h3>LLM 配置</h3>
          <el-form :model="llmConfig" label-width="100px">
            <el-form-item label="Base URL">
              <el-input v-model="llmConfig.baseUrl" placeholder="https://api.openai.com/v1" />
            </el-form-item>
            <el-form-item label="App Key">
              <el-input v-model="llmConfig.appKey" show-password />
            </el-form-item>
            <el-form-item label="模型名称">
              <el-input v-model="llmConfig.modelName" placeholder="gpt-3.5-turbo" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="testConnection">测试连接</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="config-section">
          <h3>需求输入</h3>
          <el-input
            v-model="userRequirement"
            type="textarea"
            :rows="10"
            placeholder="请输入您想要的工具组件描述，例如：我需要一个计算器组件，包含加减乘除功能，有数字按钮和操作符按钮..."
          />
          <div class="action-buttons">
            <el-button type="primary" @click="generateComponent">生成组件</el-button>
            <el-button @click="resetConfig">重置</el-button>
          </div>
        </div>
      </el-aside>

      <!-- 右侧预览区域 -->
      <el-main class="preview-panel">
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="工具渲染" name="preview">
            <div class="preview-container">
                <!-- <dynamic-component-renderer
                :component-code="componentCode"
                v-if="componentGenerated"
                /> -->
              <DynamicViewer 
                v-if="componentGenerated"
                type="html"
                :html="componentCode"
              />
              <div v-else class="empty-preview">
                <el-icon :size="50"><el-icon-picture /></el-icon>
                <p>生成的组件将在这里预览</p>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="查看代码" name="code">
            <div class="code-container">
              <div class="code-toolbar">
                <el-button type="primary" @click="copyCode" size="small">
                  <el-icon><el-icon-document-copy /></el-icon> 复制代码
                </el-button>
                <el-button @click="applyCustomCode" size="small">
                  <el-icon><el-icon-refresh /></el-icon> 应用修改
                </el-button>
              </div>
              <el-input
                v-model="componentCode"
                type="textarea"
                :rows="20"
                resize="none"
                class="code-editor"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
        <div style="padding: 10px 0; float:right;">
            <el-button @click="shareComponent">
                <el-icon><el-icon-share /></el-icon> 分享链接
            </el-button>
            <el-button type="success" @click="saveComponent">
                <el-icon><el-icon-folder-add /></el-icon> 保存组件
            </el-button>
        </div>

      </el-main>
    </el-container>

    <!-- 分享对话框 -->
    <el-dialog v-model="shareDialogVisible" title="分享组件" width="500px">
      <div>
        <p>复制以下链接分享给他人：</p>
        <el-input v-model="shareUrl" readonly>
          <template #append>
            <el-button @click="copyShareUrl">
              <el-icon><el-icon-document-copy /></el-icon>
            </el-button>
          </template>
        </el-input>
        <p class="share-tip">注意：此链接包含您的组件代码，请谨慎分享</p>
      </div>
      <template #footer>
        <el-button @click="shareDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Picture as ElIconPicture, DocumentCopy as ElIconDocumentCopy, 
  Refresh as ElIconRefresh, Share as ElIconShare, FolderAdd as ElIconFolderAdd } from '@element-plus/icons-vue'
import { simulateLLMCall } from '@/utils/componentLoader'
import DynamicViewer from './components/DynamicViewer.vue'
import { escapeVueChars, unescapeVueChars } from '@/utils/html-utils';

// import { compressHTML } from '@/utils/htmlMinifier';



export default {
  components: {
    ElIconPicture,
    ElIconDocumentCopy,
    ElIconRefresh,
    ElIconShare,
    ElIconFolderAdd,
    DynamicViewer,
  },
  setup() {
    // LLM配置
    const llmConfig = ref({
      baseUrl: '',
      appKey: '',
      modelName: '',
      testUrl: ''
    })

    // 用户需求
    const userRequirement = ref('')

    // 组件代码
    const componentCode = ref('')
    const customCode = ref('')

    // 状态管理
    const activeTab = ref('preview')
    const componentGenerated = ref(false)
    const shareDialogVisible = ref(false)
    const shareUrl = ref('')

    // const dynamicComponent = ref(null)

    // 动态组件
    const dynamicComponent = computed(() => {
      if (!componentGenerated.value) return null
    //   componentCode.value = `<div>aaa</div>`
      try {
        // 这里是一个简单的实现，实际应用中需要更安全的组件解析方式
        const component = {
          template: `<div>${componentCode.value}</div>`
        }
        return component
      } catch (e) {
        console.error('组件解析错误:', e)
        return null
      }
    })


    // 生成组件方法
    const generateComponent = async () => {
      try {
        const response = await simulateLLMCall(userRequirement.value)

        let htmlContent = unescapeVueChars(response.code ? response.code : '')

        // 压缩代码
        // const compress = async () => {
        //   let compressHTML = await compressHTML(htmlContent);
        //   console.log('压缩后的代码:', compressHTML);
        // };
  
        componentCode.value = htmlContent
        componentGenerated.value = true
        ElMessage.success('组件生成成功')

      } catch (error) {
        ElMessage.error('生成失败: ' + error.message)
      }
    }
    

    // 测试LLM连接
    const testConnection = async () => {
      try {
        // 这里应该是调用API测试连接的逻辑
        ElMessage.success('连接测试成功')
      } catch (error) {
        ElMessage.error('连接测试失败: ' + error.message)
      }
    }


    // 复制代码
    const copyCode = () => {
      navigator.clipboard.writeText(componentCode.value)
        .then(() => ElMessage.success('代码已复制'))
        .catch(() => ElMessage.error('复制失败'))
    }

    // 应用自定义代码
    const applyCustomCode = () => {

      componentGenerated.value = true
      ElMessage.success('自定义代码已应用')
      activeTab.value = 'preview'
    }

    // 分享组件
    const shareComponent = () => {
      if (!componentGenerated.value) {
        ElMessage.warning('请先生成组件')
        return
      }
      
      // 生成分享链接
      const encodedCode = encodeURIComponent(componentCode.value)
      shareUrl.value = `${window.location.origin}${window.location.pathname}?shared=true&code=${encodedCode}`
      shareDialogVisible.value = true
    }

    // 复制分享链接
    const copyShareUrl = () => {
      navigator.clipboard.writeText(shareUrl.value)
        .then(() => ElMessage.success('链接已复制'))
        .catch(() => ElMessage.error('复制失败'))
    }

    // 保存组件
    const saveComponent = () => {
      if (!componentGenerated.value) {
        ElMessage.warning('请先生成组件')
        return
      }
      
      let htmlContent = escapeVueChars(componentCode)


      // 这里应该是保存到数据库或本地存储的逻辑
      ElMessage.success('组件已保存')
    }

    // 重置配置
    const resetConfig = () => {
      llmConfig.value = {
        baseUrl: '',
        appKey: '',
        modelName: '',
        testUrl: ''
      }
      userRequirement.value = ''
      componentCode.value = ''
      customCode.value = ''
      componentGenerated.value = false
    }

    // 检查URL中是否有共享的组件代码
    const checkSharedComponent = () => {
      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.get('shared') === 'true') {
        const code = urlParams.get('code')
        if (code) {
          componentCode.value = decodeURIComponent(code)
          customCode.value = componentCode.value
          componentGenerated.value = true
          activeTab.value = 'preview'
        }
      }
    }

    // 初始化时检查共享组件
    checkSharedComponent()

    return {
      llmConfig,
      userRequirement,
      componentCode,
      customCode,
      activeTab,
      componentGenerated,
      shareDialogVisible,
      shareUrl,
      dynamicComponent,
      testConnection,
      generateComponent,
      copyCode,
      applyCustomCode,
      shareComponent,
      copyShareUrl,
      saveComponent,
      resetConfig
    }
  }
}
</script>

<style scoped>
.generator-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.config-panel {
  padding: 20px;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
}

.preview-panel {
  padding: 20px;
  height: 100%;
}

.config-section {
  margin-bottom: 30px;
}

.config-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.action-buttons {
  margin-top: 15px;
  text-align: right;
}

.preview-container {
  height: calc(100vh - 180px);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
}

.empty-preview {
  text-align: center;
  color: #999;
}

.empty-preview .el-icon {
  margin-bottom: 10px;
}

.code-container {
  height: calc(100vh - 180px);
}

.code-toolbar {
  margin-bottom: 10px;
  text-align: right;
}

.code-editor {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
}

.share-tip {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
}


</style>