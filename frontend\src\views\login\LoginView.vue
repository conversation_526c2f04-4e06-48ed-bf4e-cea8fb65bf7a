<script setup>
</script>

<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>{{ $t('login.title') }}</h2>
        <p>{{ $t('login.subtitle') }}</p>
      </div>
      
      <el-form :model="loginForm" ref="loginFormRef" :rules="rules" class="login-form">
        <el-form-item prop="username">
          <el-input 
            v-model="loginForm.username" 
            :placeholder="$t('login.username')"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            :placeholder="$t('login.password')"
            prefix-icon="Lock"
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" class="login-button" @click="handleLogin">
            {{ $t('login.loginButton') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserInfoStore } from '@/stores/userInfo'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'LoginView',
  components: {
    User,
    Lock
  },
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const userStore = useUserInfoStore()
    const loginFormRef = ref(null)
    const loading = ref(false)
    
    const loginForm = reactive({
      username: '',
      password: ''
    })
    
    const rules = {
      username: [
        { required: true, message: t('login.usernameRequired'), trigger: 'blur' }
      ],
      password: [
        { required: true, message: t('login.passwordRequired'), trigger: 'blur' }
      ]
    }
    
    const handleLogin = () => {
      loginFormRef.value.validate((valid) => {
        if (valid) {
          loading.value = true
          
          // Simulate API call
          setTimeout(() => {
            // Set user info in store
            userStore.setInfo({
              username: loginForm.username,
              // Use a placeholder avatar based on username initial
              avatar: `https://ui-avatars.com/api/?name=${loginForm.username}&background=random&color=fff`
            })
            
            loading.value = false
            ElMessage.success(t('login.loginSuccess'))
            router.push('/')
          }, 1000)
        } else {
          return false
        }
      })
    }
    
    return {
      loginForm,
      loginFormRef,
      rules,
      loading,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #ffffff;
  transition: background-color 0.3s ease;
}

.login-box {
  width: 400px;
  padding: 40px;
  border-radius: 10px;
  background-color: #f8f9fa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #dadce0;
  transition: all 0.3s ease;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  margin: 0 0 10px;
  color: #202124;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #5f6368;
}

.login-form {
  margin-top: 20px;
}

:deep(.el-input__wrapper) {
  background-color: #ffffff;
  box-shadow: none;
  border: 1px solid #dadce0;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #d2d3d4;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #1a73e8;
}

:deep(.el-input__inner) {
  color: #202124;
  background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: #9aa0a6;
}

:deep(.el-input__prefix-inner) {
  color: #5f6368;
}

:deep(.el-form-item__error) {
  color: #f56c6c;
}

.login-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px;
  background-color: #1a73e8;
  border-color: #1a73e8;
  transition: all 0.3s ease;
}

.login-button:hover {
  background-color: #1557b0;
  border-color: #1557b0;
}

.login-button.is-loading {
  background-color: #1a73e8;
  border-color: #1a73e8;
}
</style>