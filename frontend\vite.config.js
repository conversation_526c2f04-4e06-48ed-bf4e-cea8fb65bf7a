import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
// process
// import process from 'process'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      port: parseInt(env.VITE_APP_PORT) || 8012,
      proxy: {
        '/api/v1/': {
          target: env.VITE_APP_BACKEND_URL || 'http://localhost:8012',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/v1/, '/api/v1/')
        }
      }
    }
  }
})
